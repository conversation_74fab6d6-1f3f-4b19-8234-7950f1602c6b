<?php
/**
 * API endpoint to get available filter options
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database_json.php';
require_once __DIR__ . '/../includes/utils.php';

try {
    $filters = Database::getAvailableFilters();
    
    sendJsonResponse($filters);

} catch (Exception $e) {
    logError("Filters API error: " . $e->getMessage());
    sendJsonResponse(['error' => 'Failed to load filter options'], 500);
}
?>
