# Anime Conventions PHP Application

A PHP-based web application for displaying Canadian and US anime conventions. This is a complete rewrite of the original Python Flask application using only PHP, HTML, CSS, and JavaScript.

## Features

- **Convention Listings**: Display upcoming anime conventions from multiple sources
- **Search & Filtering**: Filter by country, province/state, and search by keywords
- **User Submissions**: Allow users to submit new conventions for admin approval
- **Admin Dashboard**: Approve, edit, and manage user-submitted conventions
- **Web Scraping**: Automated scraping from AnimeCons and Eventbrite
- **Responsive Design**: Mobile-friendly interface
- **API Endpoints**: JSON API for events, statistics, and filters

## Requirements

- PHP 7.4 or higher
- SQLite3 extension
- cURL extension (for web scraping)
- DOM extension (for HTML parsing)
- Web server (Apache, Nginx, or PHP built-in server)

## Installation

1. **Clone or copy the files** to your web server directory
2. **Set permissions** (if needed):
   ```bash
   chmod 755 php-animecons/
   chmod 666 php-animecons/*.db  # If database files exist
   ```
3. **Configure the application** by editing `config/config.php`:
   - Change the `SECRET_KEY` for production
   - Adjust database paths if needed
   - Set `DEBUG` to `false` for production

## Initial Setup

1. **Initialize the databases**:
   ```bash
   cd php-animecons
   php scraper.php --init-db
   ```

2. **Run the scraper** to populate with initial data:
   ```bash
   php scraper.php
   ```

3. **Access the application** in your web browser:
   - Main site: `http://your-domain/php-animecons/`
   - Admin login: `http://your-domain/php-animecons/admin/login.php`
   - Admin dashboard: `http://your-domain/php-animecons/admin/dashboard.php` (after login)

## Default Admin Credentials

- **Username**: `admin`
- **Password**: `admin`

**⚠️ Important**: Change the default admin password immediately after first login!

## Directory Structure

```
php-animecons/
├── config/
│   └── config.php              # Application configuration
├── includes/
│   ├── database.php            # Database functions
│   ├── scraper.php             # Web scraping functionality
│   └── utils.php               # Utility functions
├── templates/
│   ├── index_content.php       # Main page content template
│   └── index_script.php        # JavaScript for main page
├── admin/
│   ├── login.php               # Admin login page
│   ├── dashboard.php           # Admin dashboard
│   ├── edit.php                # Edit convention page
│   ├── approve.php             # Approve convention
│   ├── delete.php              # Delete convention
│   ├── change_password.php     # Change admin password
│   └── logout.php              # Admin logout
├── api/
│   ├── events.php              # Events API endpoint
│   ├── stats.php               # Statistics API endpoint
│   └── filters.php             # Filters API endpoint
├── assets/                     # Static assets (empty, can add CSS/JS/images)
├── index.php                   # Main application page
├── scraper.php                 # Standalone scraper script
└── README.md                   # This file
```

## Usage

### Running the Scraper

The scraper can be run manually or via cron job:

```bash
# Run scraper with default settings
php scraper.php

# Run with verbose output
php scraper.php --verbose

# Show database statistics
php scraper.php --stats

# Initialize database only
php scraper.php --init-db

# Clear old events
php scraper.php --clear-old

# Show help
php scraper.php --help
```

### Setting up Cron Job

To automatically update conventions, add a cron job:

```bash
# Run every 6 hours
0 */6 * * * cd /path/to/php-animecons && php scraper.php >/dev/null 2>&1

# Run daily at 2 AM
0 2 * * * cd /path/to/php-animecons && php scraper.php >/dev/null 2>&1
```

### API Endpoints

- **Events**: `GET /api/events.php?country=CA&province_state=Ontario`
- **Statistics**: `GET /api/stats.php`
- **Filters**: `GET /api/filters.php`

### Admin Functions

1. **Login**: Access `/admin/login.php` with admin credentials
2. **Dashboard**: View pending and approved conventions
3. **Approve**: Approve user-submitted conventions
4. **Edit**: Modify convention details
5. **Delete**: Remove conventions
6. **Change Password**: Update admin password

## Configuration Options

Edit `config/config.php` to customize:

- Database file paths
- Session settings
- Debug mode
- User agent for scraping
- State/province mappings

## Data Sources

The application scrapes data from:

1. **AnimeCons.ca** - Canadian conventions
2. **AnimeCons.com** - US and Canadian conventions  
3. **Eventbrite** - Canadian and US anime/cosplay events

## Database Schema

### Main Conventions Table
- `id`, `name`, `dates`, `location`, `source`
- `start_date`, `country`, `province_state`
- `created_at`, `updated_at`, `url`

### User Conventions Table
- Same as main table plus `is_approved` flag

### Admin Users Table
- `id`, `username`, `password`

## Security Features

- Input sanitization
- SQL injection prevention (PDO prepared statements)
- Session management
- Admin authentication
- CSRF protection (utility functions provided)

## Troubleshooting

### Common Issues

1. **Database not found**: Run `php scraper.php --init-db`
2. **Scraper fails**: Check PHP extensions (cURL, DOM)
3. **Permission errors**: Check file/directory permissions
4. **Session issues**: Ensure session directory is writable

### Debug Mode

Enable debug mode in `config/config.php`:
```php
define('DEBUG', true);
```

### Logs

Check your web server error logs for detailed error information.

## Differences from Python Version

- **No Flask**: Pure PHP with no framework dependencies
- **SQLite**: Same database format, compatible with Python version
- **Templates**: PHP templates instead of Jinja2
- **Sessions**: PHP sessions instead of Flask-Session
- **Scraping**: Native PHP cURL/DOM instead of requests/BeautifulSoup

## License

This application is provided as-is for educational and personal use.

## Support

For issues or questions, check the code comments and error logs for debugging information.
