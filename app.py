#!/usr/bin/env python3
"""
Flask web application for displaying Canadian and US anime conventions.
Data is read from a SQLite database that is populated by a separate scraper script.
"""

import logging
from datetime import datetime
from flask import Flask, render_template, jsonify, request, redirect, url_for, session
from flask_session import Session

from database import (
    init_database, init_user_database, get_upcoming_events, get_last_update_time,
    get_database_stats, get_available_filters, save_user_convention, get_approved_user_conventions,
    get_pending_user_conventions, get_all_approved_user_conventions, get_convention_by_id,
    approve_user_convention, delete_user_convention, update_user_convention, authenticate_admin, change_admin_password
)

app = Flask(__name__)
app.secret_key = 'your_secret_key_here_change_this_in_production'  # Change this in production
app.config['SESSION_TYPE'] = 'filesystem'
app.config['PERMANENT_SESSION_LIFETIME'] = 86400  # 24 hours in seconds
Session(app)

@app.route("/")
def index():
    """Display a styled table with upcoming Canadian and US anime conventions."""
    try:
        # Initialize databases if they don't exist
        init_database()
        init_user_database()

        # Get filter parameters
        country = request.args.get("country")
        province_state = request.args.get("province_state")

        # Get events from main database with filters (includes ongoing events)
        main_events = get_upcoming_events(country, province_state)

        # Get approved user-submitted events
        user_events = get_approved_user_conventions()

        # Apply filters to user events if needed
        if country:
            user_events = [e for e in user_events if e[5] == country]  # country is 6th element
        if province_state:
            user_events = [e for e in user_events if e[6] == province_state]  # province_state is 7th element

        # Combine and sort all events
        all_events = main_events + user_events
        all_events.sort(key=lambda x: x[0])  # Sort by start date

        # Get last update time
        last_update = get_last_update_time()

        # Get available filter options
        filters = get_available_filters()

        # Check if user is admin
        is_admin = session.get("admin_logged_in", False)

        return render_template(
            "index.html",
            events=all_events,
            now=datetime.now(),
            last_update=last_update,
            filters=filters,
            current_filters={
                "country": country,
                "province_state": province_state
            },
            is_admin=is_admin
        )
    except Exception as exc:
        logging.error(f"Error loading events: {exc}")
        return render_template(
            "index.html",
            events=[],
            now=datetime.now(),
            last_update=None,
            filters={},
            current_filters={},
            error="Failed to load events from database",
            is_admin=session.get("admin_logged_in", False)
        )

@app.route("/submit_convention", methods=["POST"])
def submit_convention():
    """Handle user-submitted conventions."""
    try:
        name = request.form.get("name")
        dates = request.form.get("dates")
        location = request.form.get("location")
        country = request.form.get("country")
        province_state = request.form.get("province_state")
        url = request.form.get("url", "")

        if not all([name, dates, location, country, province_state]):
            return jsonify({"success": False, "message": "All required fields must be filled"}), 400

        success = save_user_convention(name, dates, location, country, province_state, url)
        
        if success:
            return jsonify({"success": True, "message": "Convention submitted for approval!"})
        else:
            return jsonify({"success": False, "message": "Failed to save convention"}), 500
            
    except Exception as exc:
        logging.error(f"Error submitting convention: {exc}")
        return jsonify({"success": False, "message": f"Error: {exc}"}), 500

@app.route("/admin", methods=["GET", "POST"])
def admin_login():
    """Admin login page."""
    if request.method == "POST":
        username = request.form.get("username")
        password = request.form.get("password")

        if authenticate_admin(username, password):
            session.permanent = True  # Make session persistent
            session["admin_logged_in"] = True
            return redirect(url_for("admin_dashboard"))
        else:
            return render_template("admin_login.html", error="Invalid credentials")

    return render_template("admin_login.html")

@app.route("/admin/dashboard")
def admin_dashboard():
    """Admin dashboard to approve conventions."""
    if not session.get("admin_logged_in"):
        return redirect(url_for("admin_login"))

    pending_conventions = get_pending_user_conventions()
    approved_conventions = get_all_approved_user_conventions()
    return render_template("admin_dashboard.html", conventions=pending_conventions, approved_conventions=approved_conventions)

@app.route("/admin/approve/<int:convention_id>")
def approve_convention(convention_id):
    """Approve a user-submitted convention."""
    if not session.get("admin_logged_in"):
        return redirect(url_for("admin_login"))

    success = approve_user_convention(convention_id)
    if success:
        return redirect(url_for("admin_dashboard"))
    else:
        return "Failed to approve convention", 500

@app.route("/admin/delete/<int:convention_id>")
def delete_convention(convention_id):
    """Delete a user-submitted convention."""
    if not session.get("admin_logged_in"):
        return redirect(url_for("admin_login"))

    success = delete_user_convention(convention_id)
    if success:
        return redirect(url_for("admin_dashboard"))
    else:
        return "Failed to delete convention", 500

@app.route("/admin/edit/<int:convention_id>", methods=["GET", "POST"])
def edit_convention(convention_id):
    """Edit a convention."""
    if not session.get("admin_logged_in"):
        return redirect(url_for("admin_login"))

    if request.method == "POST":
        name = request.form.get("name")
        dates = request.form.get("dates")
        location = request.form.get("location")
        country = request.form.get("country")
        province_state = request.form.get("province_state")
        url = request.form.get("url", "")

        if not all([name, dates, location, country, province_state]):
            return jsonify({"success": False, "message": "All required fields must be filled"}), 400

        success = update_user_convention(convention_id, name, dates, location, country, province_state, url)
        
        if success:
            return jsonify({"success": True, "message": "Convention updated successfully!"})
        else:
            return jsonify({"success": False, "message": "Failed to update convention"}), 500

    # GET request - show edit form
    convention = get_convention_by_id(convention_id)
    if not convention:
        return "Convention not found", 404

    # Determine if this is a pending or approved convention for redirect
    is_approved = convention[9]  # is_approved is the 10th element
    redirect_url = url_for('admin_dashboard') + '#approved' if is_approved else url_for('admin_dashboard') + '#pending'

    return render_template("edit_convention.html", convention=convention, redirect_url=redirect_url)

@app.route("/admin/change_password", methods=["POST"])
def change_password():
    """Change admin password."""
    if not session.get("admin_logged_in"):
        return redirect(url_for("admin_login"))

    new_password = request.form.get("new_password")
    if new_password:
        success = change_admin_password(new_password)
        if success:
            return jsonify({"success": True, "message": "Password changed successfully!"})
        else:
            return jsonify({"success": False, "message": "Failed to change password"}), 500
    else:
        return jsonify({"success": False, "message": "New password is required"}), 400

@app.route("/admin/logout")
def admin_logout():
    """Logout admin."""
    session.pop("admin_logged_in", None)
    return redirect(url_for("admin_login"))


@app.route("/api/events")
def api_events():
    """API endpoint to get events as JSON, optionally filtered by country and province/state."""
    country = request.args.get("country")
    province_state = request.args.get("province_state")

    try:
        events = get_upcoming_events(country, province_state)

        events_data = []
        for start_date, name, dates, location, source, country, province_state, url in events:
            events_data.append({
                "name": name,
                "dates": dates,
                "location": location,
                "source": source,
                "start_date": start_date.isoformat(),
                "country": country,
                "province_state": province_state,
                "url": url
            })
        return jsonify({
            "events": events_data,
            "count": len(events_data),
            "last_update": get_last_update_time().isoformat() if get_last_update_time() else None
        })
    except Exception as exc:
        logging.error(f"API error: {exc}")
        return jsonify({"error": "Failed to load events"}), 500


@app.route("/api/stats")
def api_stats():
    """API endpoint to get database statistics."""
    try:
        stats = get_database_stats()
        return jsonify({
            "total_events": stats["total_events"],
            "by_source": stats["by_source"],
            "by_country": stats["by_country"],
            "last_update": stats["last_update"].isoformat() if stats["last_update"] else None
        })
    except Exception as exc:
        logging.error(f"Stats API error: {exc}")
        return jsonify({"error": "Failed to load statistics"}), 500


@app.route("/api/filters")
def api_filters():
    """API endpoint to get available filter options."""
    try:
        filters = get_available_filters()
        return jsonify(filters)
    except Exception as exc:
        logging.error(f"Filters API error: {exc}")
        return jsonify({"error": "Failed to load filter options"}), 500


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
    app.run(debug=True)