<?php
/**
 * Change admin password
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/utils.php';

requireAdmin();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $newPassword = sanitizeInput($_POST['new_password'] ?? '');
    
    if (empty($newPassword)) {
        sendJsonResponse(['success' => false, 'message' => 'New password is required'], 400);
    }
    
    $success = Database::changeAdminPassword($newPassword);
    
    if ($success) {
        sendJsonResponse(['success' => true, 'message' => 'Password changed successfully!']);
    } else {
        sendJsonResponse(['success' => false, 'message' => 'Failed to change password'], 500);
    }
} else {
    sendJsonResponse(['success' => false, 'message' => 'Invalid request method'], 405);
}
?>
