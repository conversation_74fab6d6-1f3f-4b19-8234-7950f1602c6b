<?php
/**
 * Approve a user-submitted convention
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/utils.php';

requireAdmin();

$conventionId = intval($_GET['id'] ?? 0);

if ($conventionId > 0) {
    $success = Database::approveUserConvention($conventionId);
    if ($success) {
        redirectWithMessage('dashboard.php', 'Convention approved successfully!', 'success');
    } else {
        redirectWithMessage('dashboard.php', 'Failed to approve convention', 'error');
    }
} else {
    redirectWithMessage('dashboard.php', 'Invalid convention ID', 'error');
}
?>
