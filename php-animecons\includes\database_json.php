<?php
/**
 * Database functions using JSON file storage (fallback for systems without SQLite)
 */

require_once __DIR__ . '/../config/config.php';

class Database {
    private static $mainDbFile = null;
    private static $userDbFile = null;
    
    /**
     * Get main database file path
     */
    private static function getMainDbFile() {
        if (self::$mainDbFile === null) {
            self::$mainDbFile = __DIR__ . '/../anime_conventions.json';
        }
        return self::$mainDbFile;
    }
    
    /**
     * Get user database file path
     */
    private static function getUserDbFile() {
        if (self::$userDbFile === null) {
            self::$userDbFile = __DIR__ . '/../user_conventions.json';
        }
        return self::$userDbFile;
    }
    
    /**
     * Load data from JSON file
     */
    private static function loadJsonData($file) {
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file_get_contents($file);
        if ($content === false) {
            return [];
        }
        
        $data = json_decode($content, true);
        return $data ?: [];
    }
    
    /**
     * Save data to JSON file
     */
    private static function saveJsonData($file, $data) {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($file, $json) !== false;
    }
    
    /**
     * Initialize the main database
     */
    public static function initDatabase() {
        $file = self::getMainDbFile();
        if (!file_exists($file)) {
            self::saveJsonData($file, [
                'conventions' => [],
                'metadata' => [
                    'created_at' => date('Y-m-d\TH:i:s'),
                    'version' => '1.0'
                ]
            ]);
        }
        error_log("Main database initialized successfully");
    }
    
    /**
     * Initialize the user database
     */
    public static function initUserDatabase() {
        $file = self::getUserDbFile();
        if (!file_exists($file)) {
            self::saveJsonData($file, [
                'user_conventions' => [],
                'admin_users' => [
                    [
                        'id' => 1,
                        'username' => 'admin',
                        'password' => 'admin'
                    ]
                ],
                'metadata' => [
                    'created_at' => date('Y-m-d\TH:i:s'),
                    'version' => '1.0'
                ]
            ]);
        }
        error_log("User database initialized successfully");
    }
    
    /**
     * Clear old events from the database
     */
    public static function clearOldEvents() {
        $data = self::loadJsonData(self::getMainDbFile());
        $currentDate = date('Y-m-d\TH:i:s');
        
        $originalCount = count($data['conventions'] ?? []);
        $data['conventions'] = array_filter($data['conventions'] ?? [], function($event) use ($currentDate) {
            return $event['start_date'] >= $currentDate;
        });
        
        $deletedCount = $originalCount - count($data['conventions']);
        self::saveJsonData(self::getMainDbFile(), $data);
        
        error_log("Deleted $deletedCount past events");
        return $deletedCount;
    }
    
    /**
     * Save events to the database, replacing existing data
     */
    public static function saveEvents($events) {
        if (empty($events)) {
            error_log("No events to save");
            return;
        }
        
        $data = [
            'conventions' => $events,
            'metadata' => [
                'updated_at' => date('Y-m-d\TH:i:s'),
                'count' => count($events)
            ]
        ];
        
        if (self::saveJsonData(self::getMainDbFile(), $data)) {
            error_log("Saved " . count($events) . " events to database");
        } else {
            throw new Exception("Failed to save events to database");
        }
    }
    
    /**
     * Get upcoming events from the database, optionally filtered by country and province/state
     */
    public static function getUpcomingEvents($country = null, $provinceState = null) {
        $data = self::loadJsonData(self::getMainDbFile());
        $conventions = $data['conventions'] ?? [];
        
        // Look back 30 days to catch ongoing multi-day events
        $lookbackDate = date('Y-m-d\TH:i:s', strtotime('-30 days'));
        $today = new DateTime();
        
        $events = [];
        
        foreach ($conventions as $row) {
            // Skip events that are too old
            if ($row['start_date'] < $lookbackDate) {
                continue;
            }
            
            // Apply filters
            if ($country && $row['country'] !== $country) {
                continue;
            }
            if ($provinceState && $row['province_state'] !== $provinceState) {
                continue;
            }
            
            $startDate = new DateTime($row['start_date']);
            
            // Only include events that are still ongoing or future
            if (self::isEventHappeningOnDate($row['dates'], $today) || $startDate > $today) {
                $events[] = [
                    'start_date' => $startDate,
                    'name' => $row['name'],
                    'dates' => $row['dates'],
                    'location' => $row['location'],
                    'source' => $row['source'],
                    'country' => $row['country'],
                    'province_state' => $row['province_state'],
                    'url' => $row['url']
                ];
            }
        }
        
        // Sort by start date
        usort($events, function($a, $b) {
            return $a['start_date'] <=> $b['start_date'];
        });
        
        error_log("Retrieved " . count($events) . " upcoming and ongoing events from database");
        return $events;
    }
    
    /**
     * Get the timestamp of the last database update
     */
    public static function getLastUpdateTime() {
        $data = self::loadJsonData(self::getMainDbFile());
        $updatedAt = $data['metadata']['updated_at'] ?? null;
        
        if ($updatedAt) {
            return new DateTime($updatedAt);
        }
        return null;
    }
    
    /**
     * Get statistics about the database
     */
    public static function getDatabaseStats() {
        $data = self::loadJsonData(self::getMainDbFile());
        $conventions = $data['conventions'] ?? [];
        
        $totalEvents = count($conventions);
        
        // Events by source
        $bySource = [];
        foreach ($conventions as $event) {
            $source = $event['source'];
            $bySource[$source] = ($bySource[$source] ?? 0) + 1;
        }
        
        // Events by country
        $byCountry = [];
        foreach ($conventions as $event) {
            $country = $event['country'];
            $byCountry[$country] = ($byCountry[$country] ?? 0) + 1;
        }
        
        $lastUpdate = self::getLastUpdateTime();
        
        return [
            'total_events' => $totalEvents,
            'by_source' => $bySource,
            'by_country' => $byCountry,
            'last_update' => $lastUpdate
        ];
    }
    
    /**
     * Get available filter options for the frontend
     */
    public static function getAvailableFilters() {
        $data = self::loadJsonData(self::getMainDbFile());
        $conventions = $data['conventions'] ?? [];
        
        $countries = [];
        $canadaProvinces = [];
        $usStates = [];
        
        foreach ($conventions as $event) {
            // Collect unique countries
            if (!in_array($event['country'], $countries)) {
                $countries[] = $event['country'];
            }
            
            // Collect provinces/states
            if ($event['country'] === 'CA' && $event['province_state'] !== 'N/A') {
                if (!in_array($event['province_state'], $canadaProvinces)) {
                    $canadaProvinces[] = $event['province_state'];
                }
            }
            
            if ($event['country'] === 'US' && $event['province_state'] !== 'N/A') {
                if (!in_array($event['province_state'], $usStates)) {
                    $usStates[] = $event['province_state'];
                }
            }
        }
        
        sort($countries);
        sort($canadaProvinces);
        sort($usStates);
        
        return [
            'countries' => $countries,
            'canada_provinces' => $canadaProvinces,
            'us_states' => $usStates
        ];
    }
    
    /**
     * Save a user-submitted convention to the user database
     */
    public static function saveUserConvention($name, $dates, $location, $country, $provinceState, $url = '') {
        try {
            $startDate = self::firstEventDate($dates);
            if (!$startDate) {
                $startDate = new DateTime('2099-12-31');
            }
            
            $currentTime = date('Y-m-d\TH:i:s');
            $data = self::loadJsonData(self::getUserDbFile());
            
            // Get next ID
            $nextId = 1;
            if (!empty($data['user_conventions'])) {
                $maxId = max(array_column($data['user_conventions'], 'id'));
                $nextId = $maxId + 1;
            }
            
            $newConvention = [
                'id' => $nextId,
                'name' => $name,
                'dates' => $dates,
                'location' => $location,
                'source' => 'User Submission',
                'start_date' => $startDate->format('Y-m-d\TH:i:s'),
                'country' => $country,
                'province_state' => $provinceState,
                'created_at' => $currentTime,
                'updated_at' => $currentTime,
                'url' => $url,
                'is_approved' => false
            ];
            
            $data['user_conventions'][] = $newConvention;
            
            if (self::saveJsonData(self::getUserDbFile(), $data)) {
                error_log("User convention saved: $name");
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Failed to save user convention: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get approved user-submitted conventions
     */
    public static function getApprovedUserConventions() {
        $data = self::loadJsonData(self::getUserDbFile());
        $userConventions = $data['user_conventions'] ?? [];
        $currentDate = date('Y-m-d\TH:i:s');
        
        $events = [];
        foreach ($userConventions as $convention) {
            if ($convention['is_approved'] && $convention['start_date'] >= $currentDate) {
                $startDate = new DateTime($convention['start_date']);
                $events[] = [
                    'start_date' => $startDate,
                    'name' => $convention['name'],
                    'dates' => $convention['dates'],
                    'location' => $convention['location'],
                    'source' => $convention['source'],
                    'country' => $convention['country'],
                    'province_state' => $convention['province_state'],
                    'url' => $convention['url']
                ];
            }
        }
        
        return $events;
    }
    
    /**
     * Get all approved user-submitted conventions (including past ones)
     */
    public static function getAllApprovedUserConventions() {
        $data = self::loadJsonData(self::getUserDbFile());
        $userConventions = $data['user_conventions'] ?? [];
        
        $approved = [];
        foreach ($userConventions as $convention) {
            if ($convention['is_approved']) {
                $approved[] = $convention;
            }
        }
        
        // Sort by start_date descending
        usort($approved, function($a, $b) {
            return strcmp($b['start_date'], $a['start_date']);
        });
        
        return $approved;
    }
    
    /**
     * Check if an event is happening on a specific date based on its dates string
     */
    public static function isEventHappeningOnDate($datesStr, $targetDate) {
        global $MONTH_NAMES;
        
        try {
            // Extract year from dates string
            if (!preg_match('/(20\d{2})/', $datesStr, $yearMatch)) {
                return false;
            }
            $year = intval($yearMatch[1]);
            
            // If the target date is not in the same year, return false
            if ($targetDate->format('Y') != $year) {
                return false;
            }
            
            $datesLower = strtolower($datesStr);
            
            // Pattern for "Month Day-Day, Year" (e.g., "July 11-13, 2025")
            if (preg_match('/(\w+)\s+(\d+)[-–](\d+)/', $datesLower, $match)) {
                $monthName = $match[1];
                $startDay = intval($match[2]);
                $endDay = intval($match[3]);
                
                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    if ($targetDate->format('n') == $month && 
                        $targetDate->format('j') >= $startDay && 
                        $targetDate->format('j') <= $endDay) {
                        return true;
                    }
                }
            }
            
            // Pattern for "Month Day, Year" (single day event)
            if (preg_match('/(\w+)\s+(\d+)(?:,|\s+' . $year . ')/', $datesLower, $match)) {
                $monthName = $match[1];
                $day = intval($match[2]);
                
                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    if ($targetDate->format('n') == $month && $targetDate->format('j') == $day) {
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error parsing date string '$datesStr': " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Return a DateTime object representing the first day mentioned in dates_str
     */
    public static function firstEventDate($datesStr) {
        global $MONTH_NAMES;
        
        // Look for a 4-digit year
        if (!preg_match('/(20\d{2})/', $datesStr, $match)) {
            return null;
        }
        $year = intval($match[1]);
        
        try {
            $monthDay = trim(explode(',', $datesStr)[0]); // "January 17-19"
            $parts = explode(' ', $monthDay, 2);
            if (count($parts) < 2) {
                return new DateTime("$year-12-31");
            }
            
            $month = $parts[0];
            $dayPart = $parts[1];
            $day = intval(preg_split('/[-–]/', $dayPart)[0]); // handle ranges "17-19"
            
            $monthLower = strtolower($month);
            if (!isset($MONTH_NAMES[$monthLower])) {
                return new DateTime("$year-12-31");
            }
            
            $monthNum = $MONTH_NAMES[$monthLower];
            return new DateTime("$year-$monthNum-$day");
            
        } catch (Exception $e) {
            return new DateTime("$year-12-31");
        }
    }

    /**
     * Get a convention by its ID
     */
    public static function getConventionById($conventionId) {
        $data = self::loadJsonData(self::getUserDbFile());
        $userConventions = $data['user_conventions'] ?? [];

        foreach ($userConventions as $convention) {
            if ($convention['id'] == $conventionId) {
                return $convention;
            }
        }

        return null;
    }

    /**
     * Update a user-submitted convention
     */
    public static function updateUserConvention($conventionId, $name, $dates, $location, $country, $provinceState, $url = '') {
        try {
            $startDate = self::firstEventDate($dates);
            if (!$startDate) {
                $startDate = new DateTime('2099-12-31');
            }

            $currentTime = date('Y-m-d\TH:i:s');
            $data = self::loadJsonData(self::getUserDbFile());

            foreach ($data['user_conventions'] as &$convention) {
                if ($convention['id'] == $conventionId) {
                    $convention['name'] = $name;
                    $convention['dates'] = $dates;
                    $convention['location'] = $location;
                    $convention['country'] = $country;
                    $convention['province_state'] = $provinceState;
                    $convention['url'] = $url;
                    $convention['start_date'] = $startDate->format('Y-m-d\TH:i:s');
                    $convention['updated_at'] = $currentTime;

                    if (self::saveJsonData(self::getUserDbFile(), $data)) {
                        error_log("Updated convention ID: $conventionId");
                        return true;
                    }
                    break;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("Failed to update convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get pending user-submitted conventions for admin approval
     */
    public static function getPendingUserConventions() {
        $data = self::loadJsonData(self::getUserDbFile());
        $userConventions = $data['user_conventions'] ?? [];

        $pending = [];
        foreach ($userConventions as $convention) {
            if (!$convention['is_approved']) {
                $pending[] = $convention;
            }
        }

        // Sort by created_at descending
        usort($pending, function($a, $b) {
            return strcmp($b['created_at'], $a['created_at']);
        });

        return $pending;
    }

    /**
     * Approve a user-submitted convention
     */
    public static function approveUserConvention($conventionId) {
        try {
            $data = self::loadJsonData(self::getUserDbFile());

            foreach ($data['user_conventions'] as &$convention) {
                if ($convention['id'] == $conventionId) {
                    $convention['is_approved'] = true;
                    $convention['updated_at'] = date('Y-m-d\TH:i:s');

                    if (self::saveJsonData(self::getUserDbFile(), $data)) {
                        error_log("Approved convention ID: $conventionId");
                        return true;
                    }
                    break;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("Failed to approve convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a user-submitted convention
     */
    public static function deleteUserConvention($conventionId) {
        try {
            $data = self::loadJsonData(self::getUserDbFile());

            $data['user_conventions'] = array_filter($data['user_conventions'], function($convention) use ($conventionId) {
                return $convention['id'] != $conventionId;
            });

            // Re-index array
            $data['user_conventions'] = array_values($data['user_conventions']);

            if (self::saveJsonData(self::getUserDbFile(), $data)) {
                error_log("Deleted convention ID: $conventionId");
                return true;
            }

            return false;

        } catch (Exception $e) {
            error_log("Failed to delete convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Authenticate admin user
     */
    public static function authenticateAdmin($username, $password) {
        $data = self::loadJsonData(self::getUserDbFile());
        $adminUsers = $data['admin_users'] ?? [];

        foreach ($adminUsers as $user) {
            if ($user['username'] === $username && $user['password'] === $password) {
                return true;
            }
        }

        return false;
    }

    /**
     * Change admin password
     */
    public static function changeAdminPassword($newPassword) {
        try {
            $data = self::loadJsonData(self::getUserDbFile());

            foreach ($data['admin_users'] as &$user) {
                if ($user['username'] === 'admin') {
                    $user['password'] = $newPassword;

                    if (self::saveJsonData(self::getUserDbFile(), $data)) {
                        error_log("Admin password changed successfully");
                        return true;
                    }
                    break;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("Failed to change admin password: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Dummy methods for compatibility (not needed for JSON storage)
     */
    public static function getMainConnection() {
        return true; // Dummy return for compatibility
    }

    public static function getUserConnection() {
        return true; // Dummy return for compatibility
    }
}
?>
