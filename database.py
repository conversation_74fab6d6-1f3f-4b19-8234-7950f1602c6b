`import sqlite3
import logging
from datetime import datetime, timedelta
from typing import List, <PERSON><PERSON>, Optional
from contextlib import contextmanager

# Database file paths
DB_PATH = "anime_conventions.db"
USER_DB_PATH = "user_conventions.db"

# Updated Event tuple type: (start_date, name, dates, location, source, country, province_state, url)
Event = Tuple[datetime, str, str, str, str, str, str, str]

def init_database():
    """Initialize the database with the required tables."""
    with sqlite3.connect(DB_PATH) as conn:
        conn.execute("""
            CREATE TABLE IF NOT EXISTS conventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                dates TEXT NOT NULL,
                location TEXT NOT NULL,
                source TEXT NOT NULL,
                start_date TEXT NOT NULL,
                country TEXT NOT NULL,
                province_state TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                url TEXT,
                UNIQUE(name, start_date, source)
            )
        """)
        
        # Create index for faster queries
        conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_start_date 
            ON conventions(start_date)
        """)
        
        conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_source 
            ON conventions(source)
        """)
        
        conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_country 
            ON conventions(country)
        """)
        
        conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_province_state 
            ON conventions(province_state)
        """)
        
        conn.commit()
        logging.info("Database initialized successfully")

def init_user_database():
    """Initialize the user conventions database with required tables."""
    with sqlite3.connect(USER_DB_PATH) as conn:
        # Table for user-submitted conventions
        conn.execute("""
            CREATE TABLE IF NOT EXISTS user_conventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                dates TEXT NOT NULL,
                location TEXT NOT NULL,
                source TEXT DEFAULT 'User Submission',
                start_date TEXT NOT NULL,
                country TEXT NOT NULL,
                province_state TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                url TEXT,
                is_approved BOOLEAN DEFAULT 0,
                UNIQUE(name, start_date)
            )
        """)

        # Table for admin users
        conn.execute("""
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL
            )
        """)

        # Insert default admin user if not exists
        conn.execute("""
            INSERT OR IGNORE INTO admin_users (username, password)
            VALUES (?, ?)
        """, ("admin", "admin"))

        conn.commit()
        logging.info("User database initialized successfully")

@contextmanager
def get_db_connection():
    """Context manager for database connections."""
    conn = sqlite3.connect(DB_PATH)
    try:
        yield conn
    finally:
        conn.close()

@contextmanager
def get_user_db_connection():
    """Context manager for user database connections."""
    conn = sqlite3.connect(USER_DB_PATH)
    try:
        yield conn
    finally:
        conn.close()

def clear_old_events():
    """Remove events that have already passed."""
    current_date = datetime.now().isoformat()
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            DELETE FROM conventions 
            WHERE start_date < ?
        """, (current_date,))
        deleted_count = cursor.rowcount
        conn.commit()
        logging.info(f"Deleted {deleted_count} past events")

def save_events(events: List[Event]):
    """Save events to the database, replacing existing data."""
    if not events:
        logging.info("No events to save")
        return
    
    current_time = datetime.now().isoformat()
    
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # Clear existing events first
        cursor.execute("DELETE FROM conventions")
        
        # Insert new events
        for start_date, name, dates, location, source, country, province_state, url in events:
            cursor.execute("""
                INSERT OR REPLACE INTO conventions 
                (name, dates, location, source, start_date, country, province_state, created_at, updated_at, url)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (name, dates, location, source, start_date.isoformat(), country, province_state, current_time, current_time, url))
        
        conn.commit()
        logging.info(f"Saved {len(events)} events to database")

def get_upcoming_events(country: Optional[str] = None, province_state: Optional[str] = None) -> List[Event]:
    """Retrieve upcoming and ongoing events from the database, optionally filtered by country and province/state."""
    # Look back 30 days to catch ongoing multi-day events
    lookback_date = (datetime.now() - timedelta(days=30)).isoformat()

    query = """
        SELECT start_date, name, dates, location, source, country, province_state, url
        FROM conventions
        WHERE start_date >= ?
    """
    params = [lookback_date]

    if country:
        query += " AND country = ?"
        params.append(country)
    if province_state:
        query += " AND province_state = ?"
        params.append(province_state)

    query += " ORDER BY start_date ASC"

    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)

        events = []
        today = datetime.now().date()

        for row in cursor.fetchall():
            start_date_str, name, dates, location, source, country, province_state, url = row
            start_date = datetime.fromisoformat(start_date_str)

            # Only include events that are still ongoing or future
            if is_event_happening_on_date(dates, today) or start_date.date() > today:
                events.append((start_date, name, dates, location, source, country, province_state, url))

        logging.info(f"Retrieved {len(events)} upcoming and ongoing events from database")
        return events

def get_events_happening_today(country: Optional[str] = None, province_state: Optional[str] = None, days_range: int = 0) -> List[Event]:
    """
    Retrieve events that are happening today or within a specified date range.

    Args:
        country: Optional country filter
        province_state: Optional province/state filter
        days_range: Number of days to look back (0 = today only, 2 = today and 2 days ago)

    Returns:
        List of events that are currently happening (started before or on today and haven't ended yet)
    """
    from datetime import datetime, timedelta

    today = datetime.now().date()

    # We need to look back much further to catch multi-day events that started earlier
    # but are still happening today. Let's look back 30 days to be safe.
    lookback_date = today - timedelta(days=30)

    # Get all events that could potentially be happening today
    # We can't rely on start_date alone since events might have started days ago
    query = """
        SELECT start_date, name, dates, location, source, country, province_state, url
        FROM conventions
        WHERE start_date >= ?
    """
    params = [lookback_date.isoformat()]

    if country:
        query += " AND country = ?"
        params.append(country)
    if province_state:
        query += " AND province_state = ?"
        params.append(province_state)

    query += " ORDER BY start_date ASC"

    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params)

        events = []
        for row in cursor.fetchall():
            start_date_str, name, dates, location, source, country, province_state, url = row

            # Check if event is happening on any day in our target range
            target_dates = [today - timedelta(days=i) for i in range(days_range + 1)]

            for target_date in target_dates:
                if is_event_happening_on_date(dates, target_date):
                    start_date_dt = datetime.fromisoformat(start_date_str)
                    events.append((start_date_dt, name, dates, location, source, country, province_state, url))
                    break  # Don't add the same event multiple times

        logging.info(f"Retrieved {len(events)} events happening in the specified date range from database")
        return events

def is_event_happening_on_date(dates_str: str, target_date) -> bool:
    """
    Check if an event is happening on a specific date based on its dates string.

    Args:
        dates_str: The dates string from the database (e.g., "July 11-13, 2025")
        target_date: The date to check (datetime.date object)

    Returns:
        True if the event is happening on the target date
    """
    import re
    from datetime import datetime

    try:
        # Extract year from dates string
        year_match = re.search(r'(20\d{2})', dates_str)
        if not year_match:
            return False
        year = int(year_match.group(1))

        # If the target date is not in the same year, return False
        if target_date.year != year:
            return False

        # Common month abbreviations and full names
        months = {
            'january': 1, 'jan': 1, 'february': 2, 'feb': 2, 'march': 3, 'mar': 3,
            'april': 4, 'apr': 4, 'may': 5, 'june': 6, 'jun': 6, 'july': 7, 'jul': 7,
            'august': 8, 'aug': 8, 'september': 9, 'sep': 9, 'sept': 9, 'october': 10, 'oct': 10,
            'november': 11, 'nov': 11, 'december': 12, 'dec': 12
        }

        # Try to parse different date formats
        dates_lower = dates_str.lower()

        # Pattern for "Month Day-Day, Year" (e.g., "July 11-13, 2025")
        pattern1 = r'(\w+)\s+(\d+)[-–](\d+)'
        match1 = re.search(pattern1, dates_lower)
        if match1:
            month_name = match1.group(1)
            start_day = int(match1.group(2))
            end_day = int(match1.group(3))

            if month_name in months:
                month = months[month_name]
                if (target_date.month == month and
                    start_day <= target_date.day <= end_day):
                    return True

        # Pattern for "Month Day, Year" (single day event)
        pattern2 = r'(\w+)\s+(\d+)(?:,|\s+' + str(year) + ')'
        match2 = re.search(pattern2, dates_lower)
        if match2:
            month_name = match2.group(1)
            day = int(match2.group(2))

            if month_name in months:
                month = months[month_name]
                if target_date.month == month and target_date.day == day:
                    return True

        # Pattern for "Month Day - Month Day, Year" (spanning months)
        pattern3 = r'(\w+)\s+(\d+)\s*[-–]\s*(\w+)\s+(\d+)'
        match3 = re.search(pattern3, dates_lower)
        if match3:
            start_month_name = match3.group(1)
            start_day = int(match3.group(2))
            end_month_name = match3.group(3)
            end_day = int(match3.group(4))

            if start_month_name in months and end_month_name in months:
                start_month = months[start_month_name]
                end_month = months[end_month_name]

                start_date = datetime(year, start_month, start_day).date()
                end_date = datetime(year, end_month, end_day).date()

                if start_date <= target_date <= end_date:
                    return True

        return False

    except Exception as e:
        logging.warning(f"Error parsing date string '{dates_str}': {e}")
        return False

def get_last_update_time() -> Optional[datetime]:
    """Get the timestamp of the last database update."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT MAX(updated_at) FROM conventions
        """)
        result = cursor.fetchone()
        if result and result[0]:
            return datetime.fromisoformat(result[0])
        return None

def get_database_stats() -> dict:
    """Get statistics about the database."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # Total events
        cursor.execute("SELECT COUNT(*) FROM conventions")
        total_events = cursor.fetchone()[0]
        
        # Events by source
        cursor.execute("""
            SELECT source, COUNT(*) 
            FROM conventions 
            GROUP BY source
        """)
        by_source = dict(cursor.fetchall())
        
        # Events by country
        cursor.execute("""
            SELECT country, COUNT(*) 
            FROM conventions 
            GROUP BY country
        """)
        by_country = dict(cursor.fetchall())
        
        # Last update
        last_update = get_last_update_time()
        
        return {
            "total_events": total_events,
            "by_source": by_source,
            "by_country": by_country,
            "last_update": last_update
        }

def get_available_filters() -> dict:
    """Get available filter options for the frontend."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        
        # Get unique countries
        cursor.execute("SELECT DISTINCT country FROM conventions ORDER BY country")
        countries = [row[0] for row in cursor.fetchall()]
        
        # Get unique provinces/states for Canada
        cursor.execute("""
            SELECT DISTINCT province_state 
            FROM conventions 
            WHERE country = 'CA' AND province_state != 'N/A'
            ORDER BY province_state
        """)
        canada_provinces = [row[0] for row in cursor.fetchall()]
        
        # Get unique states for US
        cursor.execute("""
            SELECT DISTINCT province_state 
            FROM conventions 
            WHERE country = 'US' AND province_state != 'N/A'
            ORDER BY province_state
        """)
        us_states = [row[0] for row in cursor.fetchall()]
        
        return {
            "countries": countries,
            "canada_provinces": canada_provinces,
            "us_states": us_states
        }

def save_user_convention(name: str, dates: str, location: str, country: str, province_state: str, url: str = "") -> bool:
    """Save a user-submitted convention to the user database."""
    try:
        # Parse the first date from the dates string
        start_date = first_event_date(dates)
        if not start_date:
            start_date = datetime(2099, 12, 31)  # Fallback date

        current_time = datetime.now().isoformat()

        with get_user_db_connection() as conn:
            conn.execute("""
                INSERT INTO user_conventions 
                (name, dates, location, source, start_date, country, province_state, created_at, updated_at, url, is_approved)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (name, dates, location, "User Submission", start_date.isoformat(), country, province_state, current_time, current_time, url, False))
            conn.commit()
        
        logging.info(f"User convention saved: {name}")
        return True
    except Exception as exc:
        logging.error(f"Failed to save user convention: {exc}")
        return False

def get_approved_user_conventions() -> List[Event]:
    """Get approved user-submitted conventions."""
    current_date = datetime.now().isoformat()
    with get_user_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT start_date, name, dates, location, source, country, province_state, url
            FROM user_conventions
            WHERE is_approved = 1 AND start_date >= ?
            ORDER BY start_date ASC
        """, (current_date,))
        
        events = []
        for row in cursor.fetchall():
            start_date_str, name, dates, location, source, country, province_state, url = row
            start_date = datetime.fromisoformat(start_date_str)
            events.append((start_date, name, dates, location, source, country, province_state, url))
        
        return events

def get_all_approved_user_conventions() -> List[Tuple]:
    """Get all approved user-submitted conventions (including past ones)."""
    with get_user_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, dates, location, country, province_state, url, created_at, updated_at
            FROM user_conventions
            WHERE is_approved = 1
            ORDER BY start_date DESC
        """)
        
        return cursor.fetchall()

def get_convention_by_id(convention_id: int) -> Optional[Tuple]:
    """Get a convention by its ID."""
    with get_user_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, dates, location, country, province_state, url, created_at, updated_at, is_approved
            FROM user_conventions
            WHERE id = ?
        """, (convention_id,))
        
        result = cursor.fetchone()
        return result

def update_user_convention(convention_id: int, name: str, dates: str, location: str, country: str, province_state: str, url: str = "") -> bool:
    """Update a user-submitted convention."""
    try:
        # Parse the first date from the dates string
        start_date = first_event_date(dates)
        if not start_date:
            start_date = datetime(2099, 12, 31)  # Fallback date

        current_time = datetime.now().isoformat()

        with get_user_db_connection() as conn:
            conn.execute("""
                UPDATE user_conventions 
                SET name = ?, dates = ?, location = ?, country = ?, province_state = ?, url = ?, start_date = ?, updated_at = ?
                WHERE id = ?
            """, (name, dates, location, country, province_state, url, start_date.isoformat(), current_time, convention_id))
            conn.commit()
        
        logging.info(f"Updated convention ID: {convention_id}")
        return True
    except Exception as exc:
        logging.error(f"Failed to update convention: {exc}")
        return False

def get_pending_user_conventions() -> List[Tuple]:
    """Get pending user-submitted conventions for admin approval."""
    with get_user_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, name, dates, location, country, province_state, url, created_at
            FROM user_conventions
            WHERE is_approved = 0
            ORDER BY created_at DESC
        """)
        
        return cursor.fetchall()

def approve_user_convention(convention_id: int) -> bool:
    """Approve a user-submitted convention."""
    try:
        with get_user_db_connection() as conn:
            conn.execute("UPDATE user_conventions SET is_approved = 1 WHERE id = ?", (convention_id,))
            conn.commit()
        logging.info(f"Approved convention ID: {convention_id}")
        return True
    except Exception as exc:
        logging.error(f"Failed to approve convention: {exc}")
        return False

def delete_user_convention(convention_id: int) -> bool:
    """Delete a user-submitted convention."""
    try:
        with get_user_db_connection() as conn:
            conn.execute("DELETE FROM user_conventions WHERE id = ?", (convention_id,))
            conn.commit()
        logging.info(f"Deleted convention ID: {convention_id}")
        return True
    except Exception as exc:
        logging.error(f"Failed to delete convention: {exc}")
        return False

def authenticate_admin(username: str, password: str) -> bool:
    """Authenticate admin user."""
    with get_user_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM admin_users WHERE username = ? AND password = ?", (username, password))
        return cursor.fetchone() is not None

def change_admin_password(new_password: str) -> bool:
    """Change admin password."""
    try:
        with get_user_db_connection() as conn:
            conn.execute("UPDATE admin_users SET password = ? WHERE username = 'admin'", (new_password,))
            conn.commit()
        logging.info("Admin password changed successfully")
        return True
    except Exception as exc:
        logging.error(f"Failed to change admin password: {exc}")
        return False

def first_event_date(dates_str):
    """Return a datetime object representing the first day mentioned in dates_str.
    Falls back to 12/31 of the detected year if parsing fails."""
    import re
    # Look for a 4-digit year
    m = re.search(r"(20\d{2})", dates_str)
    if not m:
        return None
    year = int(m.group(1))

    # Extract the first month and day (e.g. "January 17" from "January 17-19, 2025")
    try:
        month_day = dates_str.split(",")[0].strip()  # "January 17-19"
        month, day_part = month_day.split(" ", 1)
        day = re.split("[-–]", day_part)[0]  # handle ranges "17-19"
        first_date = datetime.strptime(f"{month} {day} {year}", "%B %d %Y")
        return first_date
    except Exception:
        # Fallback: assume event ends on Dec 31 of the year
        return datetime(year, 12, 31)

if __name__ == "__main__":
    # Initialize database when run directly
    logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
    init_database()
    init_user_database()
    print("Databases initialized successfully!")
    
    # Show current stats
    stats = get_database_stats()
    print(f"Main database stats: {stats}")