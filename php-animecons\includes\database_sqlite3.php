<?php
/**
 * Database functions using native SQLite3 class (fallback for systems without PDO SQLite)
 */

require_once __DIR__ . '/../config/config.php';

class Database {
    private static $mainDb = null;
    private static $userDb = null;
    
    /**
     * Get main database connection
     */
    public static function getMainConnection() {
        if (self::$mainDb === null) {
            try {
                if (!extension_loaded('sqlite3')) {
                    throw new Exception("SQLite3 extension is not available. Please install php-sqlite3.");
                }
                
                self::$mainDb = new SQLite3(DB_PATH);
                self::$mainDb->enableExceptions(true);
            } catch (Exception $e) {
                error_log("Database connection failed: " . $e->getMessage());
                throw $e;
            }
        }
        return self::$mainDb;
    }
    
    /**
     * Get user database connection
     */
    public static function getUserConnection() {
        if (self::$userDb === null) {
            try {
                if (!extension_loaded('sqlite3')) {
                    throw new Exception("SQLite3 extension is not available. Please install php-sqlite3.");
                }
                
                self::$userDb = new SQLite3(USER_DB_PATH);
                self::$userDb->enableExceptions(true);
            } catch (Exception $e) {
                error_log("User database connection failed: " . $e->getMessage());
                throw $e;
            }
        }
        return self::$userDb;
    }
    
    /**
     * Initialize the main database with required tables
     */
    public static function initDatabase() {
        $db = self::getMainConnection();
        
        $sql = "
            CREATE TABLE IF NOT EXISTS conventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                dates TEXT NOT NULL,
                location TEXT NOT NULL,
                source TEXT NOT NULL,
                start_date TEXT NOT NULL,
                country TEXT NOT NULL,
                province_state TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                url TEXT,
                UNIQUE(name, start_date, source)
            )
        ";
        
        $db->exec($sql);
        
        // Create indexes for faster queries
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_start_date ON conventions(start_date)",
            "CREATE INDEX IF NOT EXISTS idx_source ON conventions(source)",
            "CREATE INDEX IF NOT EXISTS idx_country ON conventions(country)",
            "CREATE INDEX IF NOT EXISTS idx_province_state ON conventions(province_state)"
        ];
        
        foreach ($indexes as $index) {
            $db->exec($index);
        }
        
        error_log("Main database initialized successfully");
    }
    
    /**
     * Initialize the user database with required tables
     */
    public static function initUserDatabase() {
        $db = self::getUserConnection();
        
        // User conventions table
        $sql = "
            CREATE TABLE IF NOT EXISTS user_conventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                dates TEXT NOT NULL,
                location TEXT NOT NULL,
                source TEXT DEFAULT 'User Submission',
                start_date TEXT NOT NULL,
                country TEXT NOT NULL,
                province_state TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                url TEXT,
                is_approved BOOLEAN DEFAULT 0,
                UNIQUE(name, start_date)
            )
        ";
        
        $db->exec($sql);
        
        // Admin users table
        $sql = "
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL
            )
        ";
        
        $db->exec($sql);
        
        // Insert default admin user if not exists
        $stmt = $db->prepare("INSERT OR IGNORE INTO admin_users (username, password) VALUES (?, ?)");
        $stmt->bindValue(1, 'admin', SQLITE3_TEXT);
        $stmt->bindValue(2, 'admin', SQLITE3_TEXT);
        $stmt->execute();
        
        error_log("User database initialized successfully");
    }
    
    /**
     * Clear old events from the database
     */
    public static function clearOldEvents() {
        $db = self::getMainConnection();
        $currentDate = date('Y-m-d\TH:i:s');
        
        $stmt = $db->prepare("DELETE FROM conventions WHERE start_date < ?");
        $stmt->bindValue(1, $currentDate, SQLITE3_TEXT);
        $result = $stmt->execute();
        
        $deletedCount = $db->changes();
        error_log("Deleted $deletedCount past events");
        
        return $deletedCount;
    }
    
    /**
     * Save events to the database, replacing existing data
     */
    public static function saveEvents($events) {
        if (empty($events)) {
            error_log("No events to save");
            return;
        }
        
        $db = self::getMainConnection();
        $currentTime = date('Y-m-d\TH:i:s');
        
        $db->exec('BEGIN TRANSACTION');
        
        try {
            // Clear existing events first
            $db->exec("DELETE FROM conventions");
            
            // Insert new events
            $stmt = $db->prepare("
                INSERT OR REPLACE INTO conventions 
                (name, dates, location, source, start_date, country, province_state, created_at, updated_at, url)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($events as $event) {
                $stmt->bindValue(1, $event['name'], SQLITE3_TEXT);
                $stmt->bindValue(2, $event['dates'], SQLITE3_TEXT);
                $stmt->bindValue(3, $event['location'], SQLITE3_TEXT);
                $stmt->bindValue(4, $event['source'], SQLITE3_TEXT);
                $stmt->bindValue(5, $event['start_date'], SQLITE3_TEXT);
                $stmt->bindValue(6, $event['country'], SQLITE3_TEXT);
                $stmt->bindValue(7, $event['province_state'], SQLITE3_TEXT);
                $stmt->bindValue(8, $currentTime, SQLITE3_TEXT);
                $stmt->bindValue(9, $currentTime, SQLITE3_TEXT);
                $stmt->bindValue(10, $event['url'], SQLITE3_TEXT);
                $stmt->execute();
                $stmt->reset();
            }
            
            $db->exec('COMMIT');
            error_log("Saved " . count($events) . " events to database");
            
        } catch (Exception $e) {
            $db->exec('ROLLBACK');
            error_log("Failed to save events: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Convert SQLite3Result to array
     */
    private static function resultToArray($result) {
        $rows = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $rows[] = $row;
        }
        return $rows;
    }
    
    /**
     * Get upcoming events from the database, optionally filtered by country and province/state
     */
    public static function getUpcomingEvents($country = null, $provinceState = null) {
        $db = self::getMainConnection();
        
        // Look back 30 days to catch ongoing multi-day events
        $lookbackDate = date('Y-m-d\TH:i:s', strtotime('-30 days'));
        
        $sql = "
            SELECT start_date, name, dates, location, source, country, province_state, url
            FROM conventions
            WHERE start_date >= ?
        ";
        $params = [$lookbackDate];
        
        if ($country) {
            $sql .= " AND country = ?";
            $params[] = $country;
        }
        if ($provinceState) {
            $sql .= " AND province_state = ?";
            $params[] = $provinceState;
        }
        
        $sql .= " ORDER BY start_date ASC";
        
        $stmt = $db->prepare($sql);
        for ($i = 0; $i < count($params); $i++) {
            $stmt->bindValue($i + 1, $params[$i], SQLITE3_TEXT);
        }
        $result = $stmt->execute();
        $rows = self::resultToArray($result);
        
        $events = [];
        $today = new DateTime();
        
        foreach ($rows as $row) {
            $startDate = new DateTime($row['start_date']);
            
            // Only include events that are still ongoing or future
            if (self::isEventHappeningOnDate($row['dates'], $today) || $startDate > $today) {
                $events[] = [
                    'start_date' => $startDate,
                    'name' => $row['name'],
                    'dates' => $row['dates'],
                    'location' => $row['location'],
                    'source' => $row['source'],
                    'country' => $row['country'],
                    'province_state' => $row['province_state'],
                    'url' => $row['url']
                ];
            }
        }
        
        error_log("Retrieved " . count($events) . " upcoming and ongoing events from database");
        return $events;
    }
    
    /**
     * Check if an event is happening on a specific date based on its dates string
     */
    public static function isEventHappeningOnDate($datesStr, $targetDate) {
        global $MONTH_NAMES;
        
        try {
            // Extract year from dates string
            if (!preg_match('/(20\d{2})/', $datesStr, $yearMatch)) {
                return false;
            }
            $year = intval($yearMatch[1]);
            
            // If the target date is not in the same year, return false
            if ($targetDate->format('Y') != $year) {
                return false;
            }
            
            $datesLower = strtolower($datesStr);
            
            // Pattern for "Month Day-Day, Year" (e.g., "July 11-13, 2025")
            if (preg_match('/(\w+)\s+(\d+)[-–](\d+)/', $datesLower, $match)) {
                $monthName = $match[1];
                $startDay = intval($match[2]);
                $endDay = intval($match[3]);
                
                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    if ($targetDate->format('n') == $month && 
                        $targetDate->format('j') >= $startDay && 
                        $targetDate->format('j') <= $endDay) {
                        return true;
                    }
                }
            }
            
            // Pattern for "Month Day, Year" (single day event)
            if (preg_match('/(\w+)\s+(\d+)(?:,|\s+' . $year . ')/', $datesLower, $match)) {
                $monthName = $match[1];
                $day = intval($match[2]);
                
                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    if ($targetDate->format('n') == $month && $targetDate->format('j') == $day) {
                        return true;
                    }
                }
            }
            
            // Pattern for "Month Day - Month Day, Year" (spanning months)
            if (preg_match('/(\w+)\s+(\d+)\s*[-–]\s*(\w+)\s+(\d+)/', $datesLower, $match)) {
                $startMonthName = $match[1];
                $startDay = intval($match[2]);
                $endMonthName = $match[3];
                $endDay = intval($match[4]);
                
                if (isset($MONTH_NAMES[$startMonthName]) && isset($MONTH_NAMES[$endMonthName])) {
                    $startMonth = $MONTH_NAMES[$startMonthName];
                    $endMonth = $MONTH_NAMES[$endMonthName];
                    
                    $startDate = new DateTime("$year-$startMonth-$startDay");
                    $endDate = new DateTime("$year-$endMonth-$endDay");
                    
                    if ($targetDate >= $startDate && $targetDate <= $endDate) {
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error parsing date string '$datesStr': " . $e->getMessage());
            return false;
        }
    }
}
?>
