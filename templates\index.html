{% extends "base.html" %}

{% block title %}Canadian & US Anime Conventions{% endblock %}

{% block additional_styles %}
      h1 {
        font-size: 2.5rem;
        font-weight: 600;
        color: #1a1a1a;
        text-align: center;
        margin-bottom: 40px;
        letter-spacing: -0.02em;
      }

      .header-actions {
        display: flex;
        justify-content: center;
        margin-bottom: 24px;
      }

      .add-convention-btn {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-family: inherit;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .add-convention-btn:hover {
        background: #2563eb;
      }

      .search-container {
        background: white;
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 24px;
      }

      .search-box {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-family: inherit;
        font-size: 1rem;
        color: #374151;
        background: white;
      }

      .search-box:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .search-box::placeholder {
        color: #9ca3af;
      }

      .scroll-to-top {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        font-size: 20px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        display: none;
        z-index: 1000;
      }

      .scroll-to-top:hover {
        background: #2563eb;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
      }

      .scroll-to-top.show {
        display: block;
      }

      .filters {
        background: white;
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        padding: 24px;
        margin-bottom: 24px;
      }

      .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
      }

      .filter-group {
        display: flex;
        flex-direction: column;
      }

      .filter-label {
        font-weight: 500;
        color: #374151;
        margin-bottom: 6px;
        font-size: 0.9rem;
      }

      .filter-select {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        font-family: inherit;
        font-size: 0.9rem;
        color: #374151;
      }

      .filter-select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .filter-actions {
        display: flex;
        align-items: center;
        gap: 16px;
        padding-top: 16px;
        border-top: 1px solid #e5e5e5;
      }

      .clear-btn {
        padding: 8px 16px;
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-family: inherit;
        font-size: 0.9rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .clear-btn:hover {
        background: #e5e7eb;
      }

      .filter-info {
        font-size: 0.85rem;
        color: #6b7280;
      }

      .table-container {
        background: white;
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        overflow: hidden;
      }

      .table-wrapper {
        overflow-x: auto;
      }

      .table {
        width: 100%;
        border-collapse: collapse;
        min-width: 800px;
      }

      .table th {
        background: #f9fafb;
        padding: 12px 16px;
        text-align: left;
        font-weight: 500;
        font-size: 0.9rem;
        color: #374151;
        border-bottom: 1px solid #e5e5e5;
      }

      .table td {
        padding: 12px 16px;
        border-bottom: 1px solid #f3f4f6;
        font-size: 0.9rem;
      }

      .table tr:nth-child(even) {
        background: #f9fafb;
      }

      .table tr:hover {
        background: #f3f4f6;
      }

      .table tr:last-child td {
        border-bottom: none;
      }

      .event-link {
        color: #3b82f6;
        text-decoration: none;
        font-weight: 500;
      }

      .event-link:hover {
        text-decoration: underline;
      }

      .badge {
        display: inline-block;
        padding: 4px 8px;
        font-size: 0.8rem;
        font-weight: 500;
        border-radius: 4px;
      }

      .country-badge {
        background: #dcfce7;
        color: #166534;
      }

      .source-badge {
        background: #fef3c7;
        color: #92400e;
      }

      .update-info {
        text-align: center;
        padding: 16px;
        font-size: 0.85rem;
        color: #6b7280;
        background: #f9fafb;
        border-top: 1px solid #e5e5e5;
      }

      .alert {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 8px;
        padding: 16px;
        margin: 24px 0;
        color: #92400e;
      }

      /* Modal Styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 0;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
      }

      .modal-header {
        padding: 20px 24px 0 24px;
        border-bottom: 1px solid #e5e5e5;
      }

      .modal-header h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 8px;
      }

      .modal-header p {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 20px;
      }

      .modal-body {
        padding: 24px;
      }

      .modal-footer {
        padding: 16px 24px 24px 24px;
        border-top: 1px solid #e5e5e5;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }

      .close {
        color: #6b7280;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
      }

      .close:hover {
        color: #374151;
      }

      .submit-form {
        background: white;
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        padding: 24px;
        margin-top: 24px;
      }

      .submit-form h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 20px;
      }

      .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group label {
        font-weight: 500;
        color: #374151;
        margin-bottom: 6px;
        font-size: 0.9rem;
      }

      .form-group input,
      .form-group select {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: white;
        font-family: inherit;
        font-size: 0.9rem;
        color: #374151;
      }

      .form-group input:focus,
      .form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .submit-btn {
        padding: 10px 20px;
        background: #3b82f6;
        color: white;
        border: none;
        border-radius: 6px;
        font-family: inherit;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .submit-btn:hover {
        background: #2563eb;
      }

      .submit-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .form-message {
        padding: 12px;
        border-radius: 6px;
        margin-top: 16px;
        font-size: 0.9rem;
      }

      .form-message.success {
        background: #dcfce7;
        color: #166534;
        border: 1px solid #bbf7d0;
      }

      .form-message.error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
      }

      .form-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid #e5e5e5;
        margin-top: 20px;
      }

      .btn-secondary {
        padding: 8px 16px;
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-family: inherit;
        font-size: 0.9rem;
        cursor: pointer;
        transition: background-color 0.2s;
        text-decoration: none;
        color: #374151;
      }

      .btn-secondary:hover {
        background: #e5e7eb;
      }

      @media (max-width: 768px) {
        .container {
          padding: 20px 16px;
        }

        h1 {
          font-size: 2rem;
        }

        .filters {
          padding: 20px;
        }

        .filters-grid {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        .filter-actions {
          flex-direction: column;
          align-items: stretch;
        }

        .clear-btn {
          width: 100%;
        }

        .table th,
        .table td {
          padding: 8px 12px;
        }

        .modal-content {
          width: 95%;
          margin: 10% auto;
        }
      }

      @media (max-width: 480px) {
        h1 {
          font-size: 1.75rem;
        }

        .filters {
          padding: 16px;
        }

        .table {
          min-width: 600px;
        }

        .table th,
        .table td {
          padding: 8px 8px;
          font-size: 0.85rem;
        }
      }
{% endblock %}

{% block content %}
    <div class="container" data-admin="{{ 'true' if is_admin else 'false' }}">
      <h1>Canadian & US Anime Conventions</h1>

      <div class="header-actions">
        <button class="add-convention-btn" onclick="openModal()">+ Add New Convention</button>
      </div>

      <div class="search-container">
        <input type="text" class="search-box" id="searchInput" placeholder="Search conventions...">
      </div>

      <div class="filters">
        <div class="filters-grid">
          <div class="filter-group">
            <label class="filter-label" for="countryFilter">Country</label>
            <select id="countryFilter" class="filter-select">
              <option value="">All Countries</option>
              {% for country in filters.countries %}
                <option value="{{ country }}" {% if current_filters.country == country %}selected{% endif %}>
                  {{ "Canada" if country == "CA" else "United States" if country == "US" else country }}
                </option>
              {% endfor %}
            </select>
          </div>
          <div class="filter-group">
            <label class="filter-label" for="provinceFilter">Province (Canada)</label>
            <select id="provinceFilter" class="filter-select">
              <option value="">All Provinces</option>
              {% for province in filters.canada_provinces %}
                <option value="{{ province }}" {% if current_filters.province_state == province %}selected{% endif %}>
                  {{ province }}
                </option>
              {% endfor %}
            </select>
          </div>
          <div class="filter-group">
            <label class="filter-label" for="stateFilter">State (US)</label>
            <select id="stateFilter" class="filter-select">
              <option value="">All States</option>
              {% for state in filters.us_states %}
                <option value="{{ state }}" {% if current_filters.province_state == state %}selected{% endif %}>
                  {{ state }}
                </option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="filter-actions">
          <button id="clearFilters" class="clear-btn">Clear Filters</button>
          <span class="filter-info" id="filterInfo"></span>
        </div>
      </div>

      {% if events %}
        <div class="table-container">
          <div class="table-wrapper">
            <table class="table">
              <thead>
                <tr>
                  <th>Event</th>
                  <th>Dates</th>
                  <th>Location</th>
                  <th>Country</th>
                  <th>Province/State</th>
                  {% if is_admin %}
                    <th>Source</th>
                  {% endif %}
                </tr>
              </thead>
              <tbody>
                {% for _start, name, dates, location, source, country, province_state, url in events %}
                  <tr>
                    <td>
                      {% if url %}
                        <a href="{{ url }}" target="_blank" rel="noopener" class="event-link">{{ name }}</a>
                      {% else %}
                        {{ name }}
                      {% endif %}
                    </td>
                    <td>{{ dates }}</td>
                    <td>{{ location }}</td>
                    <td>
                      <span class="badge country-badge">
                        {{ "Canada" if country == "CA" else "United States" if country == "US" else country }}
                      </span>
                    </td>
                    <td>{{ province_state }}</td>
                    {% if is_admin %}
                      <td><span class="badge source-badge">{{ source }}</span></td>
                    {% endif %}
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          <div class="update-info">
            {% if last_update %}
              Last updated: {{ last_update.strftime('%Y-%m-%d %H:%M') }}
            {% else %}
              No update information available
            {% endif %}
          </div>
        </div>
      {% else %}
        <div class="alert">
          {% if error %}
            {{ error }}
          {% else %}
            No upcoming conventions found.
          {% endif %}
        </div>
      {% endif %}
    </div>

    <button id="scrollToTopBtn" class="scroll-to-top" onclick="scrollToTop()">↑</button>

    <!-- Modal for adding new convention -->
    <div id="conventionModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <span class="close" onclick="closeModal()">&times;</span>
          <h2>Submit a New Convention</h2>
          <p>Help us add more conventions to our database!</p>
        </div>
        <div class="modal-body">
          <form id="submitForm" method="POST" action="{{ url_for('submit_convention') }}">
            <div class="form-grid">
              <div class="form-group">
                <label for="submitName">Event Name *</label>
                <input type="text" id="submitName" name="name" placeholder="e.g., Anime Expo 2025" required>
              </div>
              <div class="form-group">
                <label for="submitDates">Dates *</label>
                <input type="text" id="submitDates" name="dates" placeholder="e.g., July 1-4, 2025" required>
              </div>
              <div class="form-group">
                <label for="submitLocation">Location *</label>
                <input type="text" id="submitLocation" name="location" placeholder="e.g., Los Angeles Convention Center" required>
              </div>
              <div class="form-group">
                <label for="submitCountry">Country *</label>
                <select id="submitCountry" name="country" required>
                  <option value="">Select Country</option>
                  <option value="CA">Canada</option>
                  <option value="US">United States</option>
                </select>
              </div>
              <div class="form-group">
                <label for="submitProvinceState">Province/State *</label>
                <input type="text" id="submitProvinceState" name="province_state" placeholder="e.g., California or Ontario" required>
              </div>
              <div class="form-group">
                <label for="submitUrl">Event URL *</label>
                <input type="url" id="submitUrl" name="url" placeholder="https://example.com" required>
              </div>
            </div>
            <div id="submitMessage" class="form-message" style="display: none;"></div>
            <div class="form-actions">
              <button type="button" class="btn-secondary" onclick="closeModal()">Cancel</button>
              <button type="submit" class="submit-btn">Submit Convention</button>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <!-- Footer content removed since buttons are now in form -->
        </div>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        const countryFilter = document.getElementById('countryFilter');
        const provinceFilter = document.getElementById('provinceFilter');
        const stateFilter = document.getElementById('stateFilter');
        const clearFiltersBtn = document.getElementById('clearFilters');
        const filterInfo = document.getElementById('filterInfo');
        const searchInput = document.getElementById('searchInput');
        const scrollToTopBtn = document.getElementById('scrollToTopBtn');

        // Get admin status from data attribute
        const isAdmin = document.querySelector('.container').getAttribute('data-admin') === 'true';

        function updateFilters() {
          const country = countryFilter.value;
          const province = provinceFilter.value;
          const state = stateFilter.value;

          const params = new URLSearchParams();
          if (country) params.append('country', country);
          if (province) params.append('province_state', province);
          if (state) params.append('province_state', state);

          const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
          window.location.href = newUrl;
        }

        function updateFilterInfo() {
          const activeFilters = [];
          if (countryFilter.value) {
            activeFilters.push('Country: ' + countryFilter.options[countryFilter.selectedIndex].text);
          }
          if (provinceFilter.value) {
            activeFilters.push('Province: ' + provinceFilter.value);
          }
          if (stateFilter.value) {
            activeFilters.push('State: ' + stateFilter.value);
          }

          if (activeFilters.length > 0) {
            filterInfo.textContent = 'Active: ' + activeFilters.join(', ');
          } else {
            filterInfo.textContent = '';
          }
        }

        function updateFilterStates() {
          const country = countryFilter.value;
          provinceFilter.disabled = country !== 'CA';
          stateFilter.disabled = country !== 'US';

          if (country !== 'CA') provinceFilter.value = '';
          if (country !== 'US') stateFilter.value = '';

          updateFilterInfo();
        }

        countryFilter.addEventListener('change', updateFilters);
        provinceFilter.addEventListener('change', updateFilters);
        stateFilter.addEventListener('change', updateFilters);
        clearFiltersBtn.addEventListener('click', function() {
          window.location.href = window.location.pathname;
        });

        updateFilterInfo();
        updateFilterStates();
        countryFilter.addEventListener('change', updateFilterStates);

        // Search functionality
        searchInput.addEventListener('keyup', function() {
          const searchTerm = searchInput.value.toLowerCase();
          document.querySelectorAll('.table tbody tr').forEach(function(row) {
            const eventName = row.querySelector('td:first-child').textContent.toLowerCase();
            const dates = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const location = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
            const countryBadge = row.querySelector('.country-badge').textContent.toLowerCase();
            
            // Only include source badge in search if admin
            let searchableText = eventName + ' ' + dates + ' ' + location + ' ' + countryBadge;
            
            if (isAdmin) {
              const sourceBadge = row.querySelector('.source-badge');
              if (sourceBadge) {
                searchableText += ' ' + sourceBadge.textContent.toLowerCase();
              }
            }

            if (searchableText.includes(searchTerm)) {
              row.style.display = '';
            } else {
              row.style.display = 'none';
            }
          });
        });

        // Scroll to top functionality
        window.scrollToTop = function() {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }

        // Show scroll-to-top button when user scrolls down
        window.onscroll = function() {
          if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            scrollToTopBtn.classList.add('show');
          } else {
            scrollToTopBtn.classList.remove('show');
          }
        }

        // Modal functions
        window.openModal = function() {
          document.getElementById('conventionModal').style.display = 'block';
        }

        window.closeModal = function() {
          document.getElementById('conventionModal').style.display = 'none';
          document.getElementById('submitMessage').style.display = 'none';
          document.getElementById('submitForm').reset();
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
          const modal = document.getElementById('conventionModal');
          if (event.target == modal) {
            closeModal();
          }
        }

        const submitForm = document.getElementById('submitForm');
        const submitMessage = document.getElementById('submitMessage');

        submitForm.addEventListener('submit', function(event) {
          event.preventDefault();
          const formData = new FormData(submitForm);
          const message = submitMessage;

          // Show loading state
          const submitBtn = submitForm.querySelector('.submit-btn');
          const originalText = submitBtn.textContent;
          submitBtn.textContent = 'Submitting...';
          submitBtn.disabled = true;

          fetch('{{ url_for("submit_convention") }}', {
            method: 'POST',
            body: formData
          })
          .then(response => response.json())
          .then(data => {
            message.style.display = 'block';
            if (data.success) {
              message.textContent = data.message || 'Convention submitted successfully! It will be reviewed by an admin.';
              message.className = 'form-message success';
              submitForm.reset();
              // Close modal after 2 seconds
              setTimeout(closeModal, 2000);
            } else {
              message.textContent = data.message || 'Error submitting convention.';
              message.className = 'form-message error';
            }
          })
          .catch(error => {
            message.style.display = 'block';
            message.textContent = 'Error submitting convention: ' + error.message;
            message.className = 'form-message error';
          })
          .finally(() => {
            // Reset button state
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
          });
        });
      });
    </script>
{% endblock %}