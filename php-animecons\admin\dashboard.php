<?php
/**
 * Admin dashboard to approve conventions
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database_json.php';
require_once __DIR__ . '/../includes/utils.php';

requireAdmin();

$pendingConventions = Database::getPendingUserConventions();
$approvedConventions = Database::getAllApprovedUserConventions();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Anime Conventions</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #fafafa;
            color: #1a1a1a;
            line-height: 1.5;
        }

        .admin-bar {
            background: #1f2937;
            color: white;
            padding: 8px 0;
            font-size: 0.9rem;
        }

        .admin-bar-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-bar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .admin-badge {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .admin-bar a {
            color: #d1d5db;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .admin-bar a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .admin-bar .btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            text-decoration: none;
        }

        .admin-bar .btn:hover {
            background: #b91c1c;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1a1a1a;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .section {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .conventions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .conventions-table th {
            background: #f9fafb;
            padding: 12px 16px;
            text-align: left;
            font-weight: 500;
            font-size: 0.9rem;
            color: #374151;
            border-bottom: 1px solid #e5e5e5;
        }

        .conventions-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
            font-size: 0.9rem;
        }

        .conventions-table tr:nth-child(even) {
            background: #f9fafb;
        }

        .conventions-table tr:hover {
            background: #f3f4f6;
        }

        .conventions-table tr:last-child td {
            border-bottom: none;
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 0.8rem;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #e5e5e5;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            font-family: inherit;
            font-size: 0.9rem;
            cursor: pointer;
            color: #6b7280;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }

        .tab.active {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .tab:hover {
            color: #374151;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .password-form {
            background: #f9fafb;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
        }

        .password-form h3 {
            font-size: 1rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            max-width: 300px;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-family: inherit;
            font-size: 0.9rem;
            color: #374151;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .message {
            padding: 12px;
            border-radius: 6px;
            margin-top: 16px;
            font-size: 0.9rem;
            display: none;
        }

        .message.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .header {
                flex-direction: column;
                align-items: stretch;
                gap: 20px;
            }

            .header-actions {
                justify-content: center;
            }

            .conventions-table {
                font-size: 0.8rem;
            }

            .conventions-table th,
            .conventions-table td {
                padding: 8px 12px;
            }

            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-bar">
        <div class="admin-bar-content">
            <div class="admin-bar-left">
                <span class="admin-badge">ADMIN</span>
                <a href="../index.php">View Conventions</a>
                <a href="dashboard.php">Dashboard</a>
            </div>
            <div class="admin-bar-right">
                <a href="logout.php" class="btn">Logout</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>Admin Dashboard</h1>
            <div class="header-actions">
                <a href="../index.php" class="btn btn-secondary">← Back to Site</a>
            </div>
        </div>

        <div class="section">
            <div class="tabs">
                <button class="tab active" onclick="showTab('pending')">Pending Conventions (<?php echo count($pendingConventions); ?>)</button>
                <button class="tab" onclick="showTab('approved')">Approved Conventions (<?php echo count($approvedConventions); ?>)</button>
                <button class="tab" onclick="showTab('settings')">Settings</button>
            </div>

            <div id="pending" class="tab-content active">
                <h2>Pending Conventions</h2>
                <?php if (empty($pendingConventions)): ?>
                    <div class="empty-state">
                        <p>No pending conventions to review.</p>
                    </div>
                <?php else: ?>
                    <div style="overflow-x: auto;">
                        <table class="conventions-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Dates</th>
                                    <th>Location</th>
                                    <th>Country</th>
                                    <th>Province/State</th>
                                    <th>URL</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($pendingConventions as $convention): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($convention['name']); ?></td>
                                        <td><?php echo htmlspecialchars($convention['dates']); ?></td>
                                        <td><?php echo htmlspecialchars($convention['location']); ?></td>
                                        <td><?php echo getCountryDisplayName($convention['country']); ?></td>
                                        <td><?php echo htmlspecialchars($convention['province_state']); ?></td>
                                        <td>
                                            <?php if (!empty($convention['url'])): ?>
                                                <a href="<?php echo htmlspecialchars($convention['url']); ?>" target="_blank" rel="noopener">View</a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatDate($convention['created_at'], 'M j, Y'); ?></td>
                                        <td>
                                            <div class="actions">
                                                <a href="approve.php?id=<?php echo $convention['id']; ?>" class="btn btn-primary btn-small">Approve</a>
                                                <a href="edit.php?id=<?php echo $convention['id']; ?>" class="btn btn-secondary btn-small">Edit</a>
                                                <a href="delete.php?id=<?php echo $convention['id']; ?>" class="btn btn-danger btn-small" onclick="return confirm('Are you sure you want to delete this convention?')">Delete</a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <div id="approved" class="tab-content">
                <h2>Approved Conventions</h2>
                <?php if (empty($approvedConventions)): ?>
                    <div class="empty-state">
                        <p>No approved conventions yet.</p>
                    </div>
                <?php else: ?>
                    <div style="overflow-x: auto;">
                        <table class="conventions-table">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Dates</th>
                                    <th>Location</th>
                                    <th>Country</th>
                                    <th>Province/State</th>
                                    <th>URL</th>
                                    <th>Approved</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($approvedConventions as $convention): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($convention['name']); ?></td>
                                        <td><?php echo htmlspecialchars($convention['dates']); ?></td>
                                        <td><?php echo htmlspecialchars($convention['location']); ?></td>
                                        <td><?php echo getCountryDisplayName($convention['country']); ?></td>
                                        <td><?php echo htmlspecialchars($convention['province_state']); ?></td>
                                        <td>
                                            <?php if (!empty($convention['url'])): ?>
                                                <a href="<?php echo htmlspecialchars($convention['url']); ?>" target="_blank" rel="noopener">View</a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo formatDate($convention['updated_at'], 'M j, Y'); ?></td>
                                        <td>
                                            <div class="actions">
                                                <a href="edit.php?id=<?php echo $convention['id']; ?>" class="btn btn-secondary btn-small">Edit</a>
                                                <a href="delete.php?id=<?php echo $convention['id']; ?>" class="btn btn-danger btn-small" onclick="return confirm('Are you sure you want to delete this convention?')">Delete</a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>

            <div id="settings" class="tab-content">
                <h2>Settings</h2>
                <div class="password-form">
                    <h3>Change Admin Password</h3>
                    <form id="passwordForm">
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <input type="password" id="newPassword" name="new_password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Change Password</button>
                        <div id="passwordMessage" class="message"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Password change form
        document.getElementById('passwordForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            const message = document.getElementById('passwordMessage');
            
            fetch('change_password.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                message.style.display = 'block';
                if (data.success) {
                    message.textContent = data.message || 'Password changed successfully!';
                    message.className = 'message success';
                    document.getElementById('newPassword').value = '';
                } else {
                    message.textContent = data.message || 'Failed to change password';
                    message.className = 'message error';
                }
            })
            .catch(error => {
                message.style.display = 'block';
                message.textContent = 'Error: ' + error.message;
                message.className = 'message error';
            });
        });
    </script>
</body>
</html>
