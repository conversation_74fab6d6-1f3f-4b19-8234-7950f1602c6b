{% extends "base.html" %}

{% block title %}Edit Convention - Admin{% endblock %}

{% block additional_styles %}
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1a1a1a;
        }

        .header-actions {
            display: flex;
            gap: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .section {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-family: inherit;
            font-size: 0.9rem;
            color: #374151;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
        }

        .message {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 0.9rem;
        }

        .message.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .message.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .header {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .header-actions {
                justify-content: center;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
{% endblock %}

{% block content %}
    <div class="container">
        <div class="header">
            <h1>Edit Convention</h1>
            <div class="header-actions">
                <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Dashboard</a>
            </div>
        </div>

        <div class="section">
            <h2>Edit Convention Details</h2>
            <form id="editForm" method="POST">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Event Name *</label>
                        <input type="text" id="name" name="name" value="{{ convention[1] }}" required>
                    </div>
                    <div class="form-group">
                        <label for="dates">Dates *</label>
                        <input type="text" id="dates" name="dates" value="{{ convention[2] }}" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location *</label>
                        <input type="text" id="location" name="location" value="{{ convention[3] }}" required>
                    </div>
                    <div class="form-group">
                        <label for="country">Country *</label>
                        <select id="country" name="country" required>
                            <option value="">Select Country</option>
                            <option value="CA" {% if convention[4] == "CA" %}selected{% endif %}>Canada</option>
                            <option value="US" {% if convention[4] == "US" %}selected{% endif %}>United States</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="province_state">Province/State *</label>
                        <input type="text" id="province_state" name="province_state" value="{{ convention[5] }}" required>
                    </div>
                    <div class="form-group">
                        <label for="url">Event URL *</label>
                        <input type="url" id="url" name="url" value="{{ convention[6] or '' }}" required>
                    </div>
                </div>
                <div id="message" class="message" style="display: none;"></div>
                <div class="form-actions">
                    <a href="{{ redirect_url }}" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Convention</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const editForm = document.getElementById('editForm');
            const message = document.getElementById('message');

            editForm.addEventListener('submit', function(event) {
                event.preventDefault();
                const formData = new FormData(editForm);

                // Show loading state
                const submitBtn = editForm.querySelector('.btn-primary');
                const originalText = submitBtn.textContent;
                submitBtn.textContent = 'Updating...';
                submitBtn.disabled = true;

                fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    message.style.display = 'block';
                    if (data.success) {
                        message.textContent = data.message;
                        message.className = 'message success';
                        // Redirect after 2 seconds
                        setTimeout(() => {
                            window.location.href = '{{ redirect_url }}';
                        }, 2000);
                    } else {
                        message.textContent = data.message;
                        message.className = 'message error';
                    }
                })
                .catch(error => {
                    message.style.display = 'block';
                    message.textContent = 'Error updating convention: ' + error.message;
                    message.className = 'message error';
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                });
            });
        });
    </script>
{% endblock %} 