<script>
  document.addEventListener('DOMContentLoaded', function() {
    const countryFilter = document.getElementById('countryFilter');
    const provinceFilter = document.getElementById('provinceFilter');
    const stateFilter = document.getElementById('stateFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const filterInfo = document.getElementById('filterInfo');
    const searchInput = document.getElementById('searchInput');
    const scrollToTopBtn = document.getElementById('scrollToTopBtn');

    // Get admin status from data attribute
    const isAdmin = document.querySelector('.container').getAttribute('data-admin') === 'true';

    function updateFilters() {
      const country = countryFilter.value;
      const province = provinceFilter.value;
      const state = stateFilter.value;

      const params = new URLSearchParams();
      if (country) params.append('country', country);
      if (province) params.append('province_state', province);
      if (state) params.append('province_state', state);

      const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
      window.location.href = newUrl;
    }

    function updateFilterInfo() {
      const activeFilters = [];
      if (countryFilter.value) {
        activeFilters.push('Country: ' + countryFilter.options[countryFilter.selectedIndex].text);
      }
      if (provinceFilter.value) {
        activeFilters.push('Province: ' + provinceFilter.value);
      }
      if (stateFilter.value) {
        activeFilters.push('State: ' + stateFilter.value);
      }

      if (activeFilters.length > 0) {
        filterInfo.textContent = 'Active: ' + activeFilters.join(', ');
      } else {
        filterInfo.textContent = '';
      }
    }

    function updateFilterStates() {
      const country = countryFilter.value;
      provinceFilter.disabled = country !== 'CA';
      stateFilter.disabled = country !== 'US';

      if (country !== 'CA') provinceFilter.value = '';
      if (country !== 'US') stateFilter.value = '';

      updateFilterInfo();
    }

    countryFilter.addEventListener('change', updateFilters);
    provinceFilter.addEventListener('change', updateFilters);
    stateFilter.addEventListener('change', updateFilters);
    clearFiltersBtn.addEventListener('click', function() {
      window.location.href = window.location.pathname;
    });

    updateFilterInfo();
    updateFilterStates();
    countryFilter.addEventListener('change', updateFilterStates);

    // Search functionality
    searchInput.addEventListener('keyup', function() {
      const searchTerm = searchInput.value.toLowerCase();
      document.querySelectorAll('.table tbody tr').forEach(function(row) {
        const eventName = row.querySelector('td:first-child').textContent.toLowerCase();
        const dates = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const location = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const countryBadge = row.querySelector('.country-badge').textContent.toLowerCase();
        
        // Only include source badge in search if admin
        let searchableText = eventName + ' ' + dates + ' ' + location + ' ' + countryBadge;
        
        if (isAdmin) {
          const sourceBadge = row.querySelector('.source-badge');
          if (sourceBadge) {
            searchableText += ' ' + sourceBadge.textContent.toLowerCase();
          }
        }

        if (searchableText.includes(searchTerm)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    });

    // Scroll to top functionality
    window.scrollToTop = function() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }

    // Show scroll-to-top button when user scrolls down
    window.onscroll = function() {
      if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
        scrollToTopBtn.classList.add('show');
      } else {
        scrollToTopBtn.classList.remove('show');
      }
    }

    // Modal functions
    window.openModal = function() {
      document.getElementById('conventionModal').style.display = 'block';
    }

    window.closeModal = function() {
      document.getElementById('conventionModal').style.display = 'none';
      document.getElementById('submitMessage').style.display = 'none';
      document.getElementById('submitForm').reset();
    }

    // Close modal when clicking outside
    window.onclick = function(event) {
      const modal = document.getElementById('conventionModal');
      if (event.target == modal) {
        closeModal();
      }
    }

    const submitForm = document.getElementById('submitForm');
    const submitMessage = document.getElementById('submitMessage');

    submitForm.addEventListener('submit', function(event) {
      event.preventDefault();
      const formData = new FormData(submitForm);
      const message = submitMessage;

      // Show loading state
      const submitBtn = submitForm.querySelector('.submit-btn');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Submitting...';
      submitBtn.disabled = true;

      fetch(window.location.href, {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(data => {
        message.style.display = 'block';
        if (data.success) {
          message.textContent = data.message || 'Convention submitted successfully! It will be reviewed by an admin.';
          message.className = 'form-message success';
          submitForm.reset();
          // Close modal after 2 seconds
          setTimeout(closeModal, 2000);
        } else {
          message.textContent = data.message || 'Error submitting convention.';
          message.className = 'form-message error';
        }
      })
      .catch(error => {
        message.style.display = 'block';
        message.textContent = 'Error submitting convention: ' + error.message;
        message.className = 'form-message error';
      })
      .finally(() => {
        // Reset button state
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
      });
    });
  });
</script>
