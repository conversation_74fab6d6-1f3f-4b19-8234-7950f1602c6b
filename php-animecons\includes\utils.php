<?php
/**
 * Utility functions for Anime Conventions PHP application
 */

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Check if user is admin
 */
function isAdmin() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

/**
 * Require admin authentication
 */
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: admin/login.php');
        exit;
    }
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Send JSON response
 */
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit;
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'Y-m-d H:i') {
    if ($date instanceof DateTime) {
        return $date->format($format);
    }
    if (is_string($date)) {
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return $date;
        }
    }
    return '';
}

/**
 * Get country display name
 */
function getCountryDisplayName($countryCode) {
    switch ($countryCode) {
        case 'CA':
            return 'Canada';
        case 'US':
            return 'United States';
        default:
            return $countryCode;
    }
}

/**
 * Validate URL
 */
function isValidUrl($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * Validate required fields
 */
function validateRequired($fields, $data) {
    $errors = [];
    foreach ($fields as $field) {
        if (!isset($data[$field]) || trim($data[$field]) === '') {
            $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
        }
    }
    return $errors;
}

/**
 * Log error with context
 */
function logError($message, $context = []) {
    $contextStr = empty($context) ? '' : ' Context: ' . json_encode($context);
    error_log("ERROR: $message$contextStr");
}

/**
 * Log info message
 */
function logInfo($message) {
    error_log("INFO: $message");
}

/**
 * Get base URL
 */
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return "$protocol://$host$path";
}

/**
 * Include template with variables
 */
function includeTemplate($templatePath, $variables = []) {
    extract($variables);
    include $templatePath;
}

/**
 * Render template and return output
 */
function renderTemplate($templatePath, $variables = []) {
    ob_start();
    includeTemplate($templatePath, $variables);
    return ob_get_clean();
}

/**
 * Paginate array
 */
function paginate($array, $page = 1, $perPage = 20) {
    $total = count($array);
    $totalPages = ceil($total / $perPage);
    $offset = ($page - 1) * $perPage;
    $items = array_slice($array, $offset, $perPage);
    
    return [
        'items' => $items,
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_items' => $total,
        'per_page' => $perPage,
        'has_prev' => $page > 1,
        'has_next' => $page < $totalPages,
        'prev_page' => $page > 1 ? $page - 1 : null,
        'next_page' => $page < $totalPages ? $page + 1 : null
    ];
}

/**
 * Clean output buffer
 */
function cleanOutput() {
    if (ob_get_level()) {
        ob_end_clean();
    }
}
?>
