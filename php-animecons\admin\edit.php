<?php
/**
 * Edit a convention
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/utils.php';

requireAdmin();

$conventionId = intval($_GET['id'] ?? 0);
$convention = null;
$error = null;

if ($conventionId > 0) {
    $convention = Database::getConventionById($conventionId);
    if (!$convention) {
        redirectWithMessage('dashboard.php', 'Convention not found', 'error');
    }
} else {
    redirectWithMessage('dashboard.php', 'Invalid convention ID', 'error');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_convention') {
        $name = sanitizeInput($_POST['name'] ?? '');
        $dates = sanitizeInput($_POST['dates'] ?? '');
        $location = sanitizeInput($_POST['location'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $provinceState = sanitizeInput($_POST['province_state'] ?? '');
        $url = sanitizeInput($_POST['url'] ?? '');

        // Validate required fields
        $errors = validateRequired(['name', 'dates', 'location', 'country', 'province_state'], $_POST);
        
        if (!empty($errors)) {
            sendJsonResponse(['success' => false, 'message' => implode(', ', $errors)], 400);
        }

        $success = Database::updateUserConvention($conventionId, $name, $dates, $location, $country, $provinceState, $url);
        
        if ($success) {
            sendJsonResponse(['success' => true, 'message' => 'Convention updated successfully!']);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Failed to update convention'], 500);
        }
    }
}

// Determine redirect URL based on approval status
$redirectUrl = 'dashboard.php' . ($convention['is_approved'] ? '#approved' : '#pending');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Convention - Admin Dashboard</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #fafafa;
            color: #1a1a1a;
            line-height: 1.5;
        }

        .admin-bar {
            background: #1f2937;
            color: white;
            padding: 8px 0;
            font-size: 0.9rem;
        }

        .admin-bar-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-bar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .admin-badge {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .admin-bar a {
            color: #d1d5db;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .admin-bar a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .admin-bar .btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            text-decoration: none;
        }

        .admin-bar .btn:hover {
            background: #b91c1c;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1a1a1a;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .form-container {
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 8px;
            padding: 24px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 6px;
            font-size: 0.9rem;
        }

        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-family: inherit;
            font-size: 0.9rem;
            color: #374151;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
            margin-top: 20px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-primary:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        .form-message {
            padding: 12px;
            border-radius: 6px;
            margin-top: 16px;
            font-size: 0.9rem;
            display: none;
        }

        .form-message.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .form-message.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .header {
                flex-direction: column;
                align-items: stretch;
                gap: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-bar">
        <div class="admin-bar-content">
            <div class="admin-bar-left">
                <span class="admin-badge">ADMIN</span>
                <a href="../index.php">View Conventions</a>
                <a href="dashboard.php">Dashboard</a>
            </div>
            <div class="admin-bar-right">
                <a href="logout.php" class="btn">Logout</a>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>Edit Convention</h1>
            <a href="<?php echo htmlspecialchars($redirectUrl); ?>" class="btn btn-secondary">← Back to Dashboard</a>
        </div>

        <div class="form-container">
            <form id="editForm" method="POST">
                <input type="hidden" name="action" value="update_convention">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="name">Event Name *</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($convention['name']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="dates">Dates *</label>
                        <input type="text" id="dates" name="dates" value="<?php echo htmlspecialchars($convention['dates']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location *</label>
                        <input type="text" id="location" name="location" value="<?php echo htmlspecialchars($convention['location']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="country">Country *</label>
                        <select id="country" name="country" required>
                            <option value="">Select Country</option>
                            <option value="CA" <?php echo $convention['country'] === 'CA' ? 'selected' : ''; ?>>Canada</option>
                            <option value="US" <?php echo $convention['country'] === 'US' ? 'selected' : ''; ?>>United States</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="province_state">Province/State *</label>
                        <input type="text" id="province_state" name="province_state" value="<?php echo htmlspecialchars($convention['province_state']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="url">Event URL</label>
                        <input type="url" id="url" name="url" value="<?php echo htmlspecialchars($convention['url']); ?>">
                    </div>
                </div>
                <div id="editMessage" class="form-message"></div>
                <div class="form-actions">
                    <a href="<?php echo htmlspecialchars($redirectUrl); ?>" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Convention</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('editForm').addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            const message = document.getElementById('editMessage');
            const submitBtn = this.querySelector('.btn-primary');
            
            // Show loading state
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Updating...';
            submitBtn.disabled = true;

            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                message.style.display = 'block';
                if (data.success) {
                    message.textContent = data.message || 'Convention updated successfully!';
                    message.className = 'form-message success';
                    // Redirect after 2 seconds
                    setTimeout(() => {
                        window.location.href = '<?php echo htmlspecialchars($redirectUrl); ?>';
                    }, 2000);
                } else {
                    message.textContent = data.message || 'Error updating convention.';
                    message.className = 'form-message error';
                }
            })
            .catch(error => {
                message.style.display = 'block';
                message.textContent = 'Error updating convention: ' + error.message;
                message.className = 'form-message error';
            })
            .finally(() => {
                // Reset button state
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
