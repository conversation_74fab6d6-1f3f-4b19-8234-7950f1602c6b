<?php
/**
 * Database functions for Anime Conventions PHP application
 */

require_once __DIR__ . '/../config/config.php';

class Database {
    private static $mainDb = null;
    private static $userDb = null;
    
    /**
     * Get main database connection
     */
    public static function getMainConnection() {
        if (self::$mainDb === null) {
            try {
                self::$mainDb = new PDO('sqlite:' . DB_PATH);
                self::$mainDb->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                self::$mainDb->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                error_log("Database connection failed: " . $e->getMessage());
                throw $e;
            }
        }
        return self::$mainDb;
    }
    
    /**
     * Get user database connection
     */
    public static function getUserConnection() {
        if (self::$userDb === null) {
            try {
                self::$userDb = new PDO('sqlite:' . USER_DB_PATH);
                self::$userDb->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                self::$userDb->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                error_log("User database connection failed: " . $e->getMessage());
                throw $e;
            }
        }
        return self::$userDb;
    }
    
    /**
     * Initialize the main database with required tables
     */
    public static function initDatabase() {
        $db = self::getMainConnection();
        
        $sql = "
            CREATE TABLE IF NOT EXISTS conventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                dates TEXT NOT NULL,
                location TEXT NOT NULL,
                source TEXT NOT NULL,
                start_date TEXT NOT NULL,
                country TEXT NOT NULL,
                province_state TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                url TEXT,
                UNIQUE(name, start_date, source)
            )
        ";
        
        $db->exec($sql);
        
        // Create indexes for faster queries
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_start_date ON conventions(start_date)",
            "CREATE INDEX IF NOT EXISTS idx_source ON conventions(source)",
            "CREATE INDEX IF NOT EXISTS idx_country ON conventions(country)",
            "CREATE INDEX IF NOT EXISTS idx_province_state ON conventions(province_state)"
        ];
        
        foreach ($indexes as $index) {
            $db->exec($index);
        }
        
        error_log("Main database initialized successfully");
    }
    
    /**
     * Initialize the user database with required tables
     */
    public static function initUserDatabase() {
        $db = self::getUserConnection();
        
        // User conventions table
        $sql = "
            CREATE TABLE IF NOT EXISTS user_conventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                dates TEXT NOT NULL,
                location TEXT NOT NULL,
                source TEXT DEFAULT 'User Submission',
                start_date TEXT NOT NULL,
                country TEXT NOT NULL,
                province_state TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                url TEXT,
                is_approved BOOLEAN DEFAULT 0,
                UNIQUE(name, start_date)
            )
        ";
        
        $db->exec($sql);
        
        // Admin users table
        $sql = "
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password TEXT NOT NULL
            )
        ";
        
        $db->exec($sql);
        
        // Insert default admin user if not exists
        $stmt = $db->prepare("INSERT OR IGNORE INTO admin_users (username, password) VALUES (?, ?)");
        $stmt->execute(['admin', 'admin']);
        
        error_log("User database initialized successfully");
    }
    
    /**
     * Clear old events from the database
     */
    public static function clearOldEvents() {
        $db = self::getMainConnection();
        $currentDate = date('Y-m-d\TH:i:s');
        
        $stmt = $db->prepare("DELETE FROM conventions WHERE start_date < ?");
        $stmt->execute([$currentDate]);
        
        $deletedCount = $stmt->rowCount();
        error_log("Deleted $deletedCount past events");
        
        return $deletedCount;
    }
    
    /**
     * Save events to the database, replacing existing data
     */
    public static function saveEvents($events) {
        if (empty($events)) {
            error_log("No events to save");
            return;
        }
        
        $db = self::getMainConnection();
        $currentTime = date('Y-m-d\TH:i:s');
        
        $db->beginTransaction();
        
        try {
            // Clear existing events first
            $db->exec("DELETE FROM conventions");
            
            // Insert new events
            $stmt = $db->prepare("
                INSERT OR REPLACE INTO conventions 
                (name, dates, location, source, start_date, country, province_state, created_at, updated_at, url)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            foreach ($events as $event) {
                $stmt->execute([
                    $event['name'],
                    $event['dates'],
                    $event['location'],
                    $event['source'],
                    $event['start_date'],
                    $event['country'],
                    $event['province_state'],
                    $currentTime,
                    $currentTime,
                    $event['url']
                ]);
            }
            
            $db->commit();
            error_log("Saved " . count($events) . " events to database");
            
        } catch (Exception $e) {
            $db->rollBack();
            error_log("Failed to save events: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get upcoming events from the database, optionally filtered by country and province/state
     */
    public static function getUpcomingEvents($country = null, $provinceState = null) {
        $db = self::getMainConnection();

        // Look back 30 days to catch ongoing multi-day events
        $lookbackDate = date('Y-m-d\TH:i:s', strtotime('-30 days'));

        $sql = "
            SELECT start_date, name, dates, location, source, country, province_state, url
            FROM conventions
            WHERE start_date >= ?
        ";
        $params = [$lookbackDate];

        if ($country) {
            $sql .= " AND country = ?";
            $params[] = $country;
        }
        if ($provinceState) {
            $sql .= " AND province_state = ?";
            $params[] = $provinceState;
        }

        $sql .= " ORDER BY start_date ASC";

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll();

        $events = [];
        $today = new DateTime();

        foreach ($rows as $row) {
            $startDate = new DateTime($row['start_date']);

            // Only include events that are still ongoing or future
            if (self::isEventHappeningOnDate($row['dates'], $today) || $startDate > $today) {
                $events[] = [
                    'start_date' => $startDate,
                    'name' => $row['name'],
                    'dates' => $row['dates'],
                    'location' => $row['location'],
                    'source' => $row['source'],
                    'country' => $row['country'],
                    'province_state' => $row['province_state'],
                    'url' => $row['url']
                ];
            }
        }

        error_log("Retrieved " . count($events) . " upcoming and ongoing events from database");
        return $events;
    }

    /**
     * Get events happening today or within a specified date range
     */
    public static function getEventsHappeningToday($country = null, $provinceState = null, $daysRange = 0) {
        $db = self::getMainConnection();
        $today = new DateTime();

        // Look back 30 days to catch multi-day events that started earlier
        $lookbackDate = date('Y-m-d\TH:i:s', strtotime('-30 days'));

        $sql = "
            SELECT start_date, name, dates, location, source, country, province_state, url
            FROM conventions
            WHERE start_date >= ?
        ";
        $params = [$lookbackDate];

        if ($country) {
            $sql .= " AND country = ?";
            $params[] = $country;
        }
        if ($provinceState) {
            $sql .= " AND province_state = ?";
            $params[] = $provinceState;
        }

        $sql .= " ORDER BY start_date ASC";

        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $rows = $stmt->fetchAll();

        $events = [];
        for ($i = 0; $i <= $daysRange; $i++) {
            $targetDate = clone $today;
            $targetDate->sub(new DateInterval("P{$i}D"));

            foreach ($rows as $row) {
                if (self::isEventHappeningOnDate($row['dates'], $targetDate)) {
                    $startDate = new DateTime($row['start_date']);
                    $events[] = [
                        'start_date' => $startDate,
                        'name' => $row['name'],
                        'dates' => $row['dates'],
                        'location' => $row['location'],
                        'source' => $row['source'],
                        'country' => $row['country'],
                        'province_state' => $row['province_state'],
                        'url' => $row['url']
                    ];
                    break; // Don't add the same event multiple times
                }
            }
        }

        error_log("Retrieved " . count($events) . " events happening in the specified date range from database");
        return $events;
    }

    /**
     * Check if an event is happening on a specific date based on its dates string
     */
    public static function isEventHappeningOnDate($datesStr, $targetDate) {
        global $MONTH_NAMES;

        try {
            // Extract year from dates string
            if (!preg_match('/(20\d{2})/', $datesStr, $yearMatch)) {
                return false;
            }
            $year = intval($yearMatch[1]);

            // If the target date is not in the same year, return false
            if ($targetDate->format('Y') != $year) {
                return false;
            }

            $datesLower = strtolower($datesStr);

            // Pattern for "Month Day-Day, Year" (e.g., "July 11-13, 2025")
            if (preg_match('/(\w+)\s+(\d+)[-–](\d+)/', $datesLower, $match)) {
                $monthName = $match[1];
                $startDay = intval($match[2]);
                $endDay = intval($match[3]);

                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    if ($targetDate->format('n') == $month &&
                        $targetDate->format('j') >= $startDay &&
                        $targetDate->format('j') <= $endDay) {
                        return true;
                    }
                }
            }

            // Pattern for "Month Day, Year" (single day event)
            if (preg_match('/(\w+)\s+(\d+)(?:,|\s+' . $year . ')/', $datesLower, $match)) {
                $monthName = $match[1];
                $day = intval($match[2]);

                if (isset($MONTH_NAMES[$monthName])) {
                    $month = $MONTH_NAMES[$monthName];
                    if ($targetDate->format('n') == $month && $targetDate->format('j') == $day) {
                        return true;
                    }
                }
            }

            // Pattern for "Month Day - Month Day, Year" (spanning months)
            if (preg_match('/(\w+)\s+(\d+)\s*[-–]\s*(\w+)\s+(\d+)/', $datesLower, $match)) {
                $startMonthName = $match[1];
                $startDay = intval($match[2]);
                $endMonthName = $match[3];
                $endDay = intval($match[4]);

                if (isset($MONTH_NAMES[$startMonthName]) && isset($MONTH_NAMES[$endMonthName])) {
                    $startMonth = $MONTH_NAMES[$startMonthName];
                    $endMonth = $MONTH_NAMES[$endMonthName];

                    $startDate = new DateTime("$year-$startMonth-$startDay");
                    $endDate = new DateTime("$year-$endMonth-$endDay");

                    if ($targetDate >= $startDate && $targetDate <= $endDate) {
                        return true;
                    }
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("Error parsing date string '$datesStr': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the timestamp of the last database update
     */
    public static function getLastUpdateTime() {
        $db = self::getMainConnection();

        $stmt = $db->prepare("SELECT MAX(updated_at) as last_update FROM conventions");
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result && $result['last_update']) {
            return new DateTime($result['last_update']);
        }
        return null;
    }

    /**
     * Get statistics about the database
     */
    public static function getDatabaseStats() {
        $db = self::getMainConnection();

        // Total events
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM conventions");
        $stmt->execute();
        $totalEvents = $stmt->fetch()['total'];

        // Events by source
        $stmt = $db->prepare("SELECT source, COUNT(*) as count FROM conventions GROUP BY source");
        $stmt->execute();
        $bySource = [];
        while ($row = $stmt->fetch()) {
            $bySource[$row['source']] = $row['count'];
        }

        // Events by country
        $stmt = $db->prepare("SELECT country, COUNT(*) as count FROM conventions GROUP BY country");
        $stmt->execute();
        $byCountry = [];
        while ($row = $stmt->fetch()) {
            $byCountry[$row['country']] = $row['count'];
        }

        $lastUpdate = self::getLastUpdateTime();

        return [
            'total_events' => $totalEvents,
            'by_source' => $bySource,
            'by_country' => $byCountry,
            'last_update' => $lastUpdate
        ];
    }

    /**
     * Get available filter options for the frontend
     */
    public static function getAvailableFilters() {
        $db = self::getMainConnection();

        // Get unique countries
        $stmt = $db->prepare("SELECT DISTINCT country FROM conventions ORDER BY country");
        $stmt->execute();
        $countries = [];
        while ($row = $stmt->fetch()) {
            $countries[] = $row['country'];
        }

        // Get unique provinces/states for Canada
        $stmt = $db->prepare("
            SELECT DISTINCT province_state
            FROM conventions
            WHERE country = 'CA' AND province_state != 'N/A'
            ORDER BY province_state
        ");
        $stmt->execute();
        $canadaProvinces = [];
        while ($row = $stmt->fetch()) {
            $canadaProvinces[] = $row['province_state'];
        }

        // Get unique states for US
        $stmt = $db->prepare("
            SELECT DISTINCT province_state
            FROM conventions
            WHERE country = 'US' AND province_state != 'N/A'
            ORDER BY province_state
        ");
        $stmt->execute();
        $usStates = [];
        while ($row = $stmt->fetch()) {
            $usStates[] = $row['province_state'];
        }

        return [
            'countries' => $countries,
            'canada_provinces' => $canadaProvinces,
            'us_states' => $usStates
        ];
    }

    /**
     * Save a user-submitted convention to the user database
     */
    public static function saveUserConvention($name, $dates, $location, $country, $provinceState, $url = '') {
        try {
            $startDate = self::firstEventDate($dates);
            if (!$startDate) {
                $startDate = new DateTime('2099-12-31');
            }

            $currentTime = date('Y-m-d\TH:i:s');
            $db = self::getUserConnection();

            $stmt = $db->prepare("
                INSERT INTO user_conventions
                (name, dates, location, source, start_date, country, province_state, created_at, updated_at, url, is_approved)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $name, $dates, $location, 'User Submission',
                $startDate->format('Y-m-d\TH:i:s'), $country, $provinceState,
                $currentTime, $currentTime, $url, 0
            ]);

            error_log("User convention saved: $name");
            return true;

        } catch (Exception $e) {
            error_log("Failed to save user convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get approved user-submitted conventions
     */
    public static function getApprovedUserConventions() {
        $db = self::getUserConnection();
        $currentDate = date('Y-m-d\TH:i:s');

        $stmt = $db->prepare("
            SELECT start_date, name, dates, location, source, country, province_state, url
            FROM user_conventions
            WHERE is_approved = 1 AND start_date >= ?
            ORDER BY start_date ASC
        ");
        $stmt->execute([$currentDate]);

        $events = [];
        while ($row = $stmt->fetch()) {
            $startDate = new DateTime($row['start_date']);
            $events[] = [
                'start_date' => $startDate,
                'name' => $row['name'],
                'dates' => $row['dates'],
                'location' => $row['location'],
                'source' => $row['source'],
                'country' => $row['country'],
                'province_state' => $row['province_state'],
                'url' => $row['url']
            ];
        }

        return $events;
    }

    /**
     * Get all approved user-submitted conventions (including past ones)
     */
    public static function getAllApprovedUserConventions() {
        $db = self::getUserConnection();

        $stmt = $db->prepare("
            SELECT id, name, dates, location, country, province_state, url, created_at, updated_at
            FROM user_conventions
            WHERE is_approved = 1
            ORDER BY start_date DESC
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Get a convention by its ID
     */
    public static function getConventionById($conventionId) {
        $db = self::getUserConnection();

        $stmt = $db->prepare("
            SELECT id, name, dates, location, country, province_state, url, created_at, updated_at, is_approved
            FROM user_conventions
            WHERE id = ?
        ");
        $stmt->execute([$conventionId]);

        return $stmt->fetch();
    }

    /**
     * Update a user-submitted convention
     */
    public static function updateUserConvention($conventionId, $name, $dates, $location, $country, $provinceState, $url = '') {
        try {
            $startDate = self::firstEventDate($dates);
            if (!$startDate) {
                $startDate = new DateTime('2099-12-31');
            }

            $currentTime = date('Y-m-d\TH:i:s');
            $db = self::getUserConnection();

            $stmt = $db->prepare("
                UPDATE user_conventions
                SET name = ?, dates = ?, location = ?, country = ?, province_state = ?, url = ?, start_date = ?, updated_at = ?
                WHERE id = ?
            ");

            $stmt->execute([
                $name, $dates, $location, $country, $provinceState, $url,
                $startDate->format('Y-m-d\TH:i:s'), $currentTime, $conventionId
            ]);

            error_log("Updated convention ID: $conventionId");
            return true;

        } catch (Exception $e) {
            error_log("Failed to update convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get pending user-submitted conventions for admin approval
     */
    public static function getPendingUserConventions() {
        $db = self::getUserConnection();

        $stmt = $db->prepare("
            SELECT id, name, dates, location, country, province_state, url, created_at
            FROM user_conventions
            WHERE is_approved = 0
            ORDER BY created_at DESC
        ");
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Approve a user-submitted convention
     */
    public static function approveUserConvention($conventionId) {
        try {
            $db = self::getUserConnection();
            $stmt = $db->prepare("UPDATE user_conventions SET is_approved = 1 WHERE id = ?");
            $stmt->execute([$conventionId]);

            error_log("Approved convention ID: $conventionId");
            return true;

        } catch (Exception $e) {
            error_log("Failed to approve convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete a user-submitted convention
     */
    public static function deleteUserConvention($conventionId) {
        try {
            $db = self::getUserConnection();
            $stmt = $db->prepare("DELETE FROM user_conventions WHERE id = ?");
            $stmt->execute([$conventionId]);

            error_log("Deleted convention ID: $conventionId");
            return true;

        } catch (Exception $e) {
            error_log("Failed to delete convention: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Authenticate admin user
     */
    public static function authenticateAdmin($username, $password) {
        $db = self::getUserConnection();

        $stmt = $db->prepare("SELECT * FROM admin_users WHERE username = ? AND password = ?");
        $stmt->execute([$username, $password]);

        return $stmt->fetch() !== false;
    }

    /**
     * Change admin password
     */
    public static function changeAdminPassword($newPassword) {
        try {
            $db = self::getUserConnection();
            $stmt = $db->prepare("UPDATE admin_users SET password = ? WHERE username = 'admin'");
            $stmt->execute([$newPassword]);

            error_log("Admin password changed successfully");
            return true;

        } catch (Exception $e) {
            error_log("Failed to change admin password: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Return a DateTime object representing the first day mentioned in dates_str
     * Falls back to 12/31 of the detected year if parsing fails
     */
    public static function firstEventDate($datesStr) {
        global $MONTH_NAMES;

        // Look for a 4-digit year
        if (!preg_match('/(20\d{2})/', $datesStr, $match)) {
            return null;
        }
        $year = intval($match[1]);

        // Extract the first month and day (e.g. "January 17" from "January 17-19, 2025")
        try {
            $monthDay = trim(explode(',', $datesStr)[0]); // "January 17-19"
            $parts = explode(' ', $monthDay, 2);
            if (count($parts) < 2) {
                return new DateTime("$year-12-31");
            }

            $month = $parts[0];
            $dayPart = $parts[1];
            $day = intval(preg_split('/[-–]/', $dayPart)[0]); // handle ranges "17-19"

            $monthLower = strtolower($month);
            if (!isset($MONTH_NAMES[$monthLower])) {
                return new DateTime("$year-12-31");
            }

            $monthNum = $MONTH_NAMES[$monthLower];
            return new DateTime("$year-$monthNum-$day");

        } catch (Exception $e) {
            // Fallback: assume event ends on Dec 31 of the year
            return new DateTime("$year-12-31");
        }
    }
}
?>
