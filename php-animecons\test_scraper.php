<?php
/**
 * Test script to debug scraper issues
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/scraper.php';

// Test a simple URL fetch first
echo "Testing URL fetch...\n";

try {
    // Use reflection to access private method
    $reflection = new ReflectionClass('Scraper');
    $fetchUrlMethod = $reflection->getMethod('fetchUrl');
    $fetchUrlMethod->setAccessible(true);
    
    $testUrl = 'https://animecons.ca/events/schedule.php?loc=ca';
    echo "Fetching: $testUrl\n";
    
    $html = $fetchUrlMethod->invoke(null, $testUrl);
    
    if ($html) {
        echo "SUCCESS: Fetched " . strlen($html) . " bytes\n";

        // Show first 500 characters of HTML to debug
        echo "HTML preview:\n";
        echo substr($html, 0, 500) . "\n";
        echo "...\n\n";

        // Check if we can find table elements
        $dom = new DOMDocument();
        @$dom->loadHTML($html);
        $xpath = new DOMXPath($dom);

        $tables = $xpath->query("//table");
        echo "Found " . $tables->length . " tables\n";

        // Also check for any div or other elements that might contain the data
        $divs = $xpath->query("//div");
        echo "Found " . $divs->length . " divs\n";

        // Look for any text containing "convention" or "anime"
        $bodyText = $xpath->query("//body")->item(0);
        if ($bodyText) {
            $fullText = $bodyText->textContent;
            echo "Body text length: " . strlen($fullText) . "\n";
            if (stripos($fullText, 'convention') !== false) {
                echo "Found 'convention' in text\n";
            }
            if (stripos($fullText, 'anime') !== false) {
                echo "Found 'anime' in text\n";
            }
        }
        
        foreach ($tables as $i => $table) {
            $headerRow = $xpath->query(".//tr", $table)->item(0);
            if ($headerRow) {
                $headerText = $headerRow->textContent;
                echo "Table $i header: " . trim($headerText) . "\n";
                
                if (strpos($headerText, 'Dates') !== false && strpos($headerText, 'Location') !== false) {
                    echo "  -> This looks like an events table!\n";
                    
                    $rows = $xpath->query(".//tr", $table);
                    echo "  -> Found " . ($rows->length - 1) . " data rows\n";
                    
                    // Show first few data rows
                    for ($j = 1; $j < min(4, $rows->length); $j++) {
                        $row = $rows->item($j);
                        $cells = $xpath->query(".//td", $row);
                        if ($cells->length >= 3) {
                            $name = trim($cells->item(0)->textContent);
                            $dates = trim($cells->item(1)->textContent);
                            $location = trim($cells->item(2)->textContent);
                            echo "    Row $j: $name | $dates | $location\n";
                        }
                    }
                }
            }
        }
        
    } else {
        echo "FAILED: No content returned\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>
