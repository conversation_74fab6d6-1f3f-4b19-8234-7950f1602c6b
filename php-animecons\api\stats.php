<?php
/**
 * API endpoint to get database statistics
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database_json.php';
require_once __DIR__ . '/../includes/utils.php';

try {
    $stats = Database::getDatabaseStats();
    
    sendJsonResponse([
        'total_events' => $stats['total_events'],
        'by_source' => $stats['by_source'],
        'by_country' => $stats['by_country'],
        'last_update' => $stats['last_update'] ? $stats['last_update']->format('c') : null
    ]);

} catch (Exception $e) {
    logError("Stats API error: " . $e->getMessage());
    sendJsonResponse(['error' => 'Failed to load statistics'], 500);
}
?>
