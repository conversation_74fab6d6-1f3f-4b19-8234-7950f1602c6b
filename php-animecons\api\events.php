<?php
/**
 * API endpoint to get events as JSON, optionally filtered by country and province/state
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/utils.php';

try {
    $country = isset($_GET['country']) ? sanitizeInput($_GET['country']) : null;
    $provinceState = isset($_GET['province_state']) ? sanitizeInput($_GET['province_state']) : null;

    $events = Database::getUpcomingEvents($country, $provinceState);

    $eventsData = [];
    foreach ($events as $event) {
        $eventsData[] = [
            'name' => $event['name'],
            'dates' => $event['dates'],
            'location' => $event['location'],
            'source' => $event['source'],
            'start_date' => $event['start_date']->format('c'), // ISO 8601 format
            'country' => $event['country'],
            'province_state' => $event['province_state'],
            'url' => $event['url']
        ];
    }

    $lastUpdate = Database::getLastUpdateTime();
    
    sendJsonResponse([
        'events' => $eventsData,
        'count' => count($eventsData),
        'last_update' => $lastUpdate ? $lastUpdate->format('c') : null
    ]);

} catch (Exception $e) {
    logError("API error: " . $e->getMessage());
    sendJsonResponse(['error' => 'Failed to load events'], 500);
}
?>
