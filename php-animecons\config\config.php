<?php
/**
 * Configuration file for Anime Conventions PHP application
 */

// Database configuration (JSON fallback)
define('DB_PATH', __DIR__ . '/../anime_conventions.json');
define('USER_DB_PATH', __DIR__ . '/../user_conventions.json');

// Application settings
define('APP_NAME', 'Anime Conventions Directory');
define('APP_VERSION', '1.0.0');
define('DEBUG', false);

// Session configuration
define('SESSION_LIFETIME', 86400); // 24 hours in seconds
define('SESSION_NAME', 'animecons_session');

// Security settings
define('SECRET_KEY', 'your_secret_key_here_change_this_in_production');

// HTTP headers for scraping
define('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0 Safari/537.36');

// US State abbreviations to full names
$US_STATE_ABBR_TO_NAME = [
    "AL" => "Alabama", "AK" => "Alaska", "AZ" => "Arizona", "AR" => "Arkansas", 
    "CA" => "California", "CO" => "Colorado", "CT" => "Connecticut", "DE" => "Delaware", 
    "FL" => "Florida", "GA" => "Georgia", "HI" => "Hawaii", "ID" => "Idaho", 
    "IL" => "Illinois", "IN" => "Indiana", "IA" => "Iowa", "KS" => "Kansas", 
    "KY" => "Kentucky", "LA" => "Louisiana", "ME" => "Maine", "MD" => "Maryland", 
    "MA" => "Massachusetts", "MI" => "Michigan", "MN" => "Minnesota", "MS" => "Mississippi", 
    "MO" => "Missouri", "MT" => "Montana", "NE" => "Nebraska", "NV" => "Nevada", 
    "NH" => "New Hampshire", "NJ" => "New Jersey", "NM" => "New Mexico", "NY" => "New York", 
    "NC" => "North Carolina", "ND" => "North Dakota", "OH" => "Ohio", "OK" => "Oklahoma", 
    "OR" => "Oregon", "PA" => "Pennsylvania", "RI" => "Rhode Island", "SC" => "South Carolina", 
    "SD" => "South Dakota", "TN" => "Tennessee", "TX" => "Texas", "UT" => "Utah", 
    "VT" => "Vermont", "VA" => "Virginia", "WA" => "Washington", "WV" => "West Virginia", 
    "WI" => "Wisconsin", "WY" => "Wyoming"
];

// Canadian Province abbreviations to full names
$CA_PROVINCE_ABBR_TO_NAME = [
    "AB" => "Alberta", "BC" => "British Columbia", "MB" => "Manitoba", 
    "NB" => "New Brunswick", "NL" => "Newfoundland and Labrador", "NS" => "Nova Scotia", 
    "NT" => "Northwest Territories", "NU" => "Nunavut", "ON" => "Ontario", 
    "PE" => "Prince Edward Island", "QC" => "Quebec", "SK" => "Saskatchewan", 
    "YT" => "Yukon"
];

// Month names for date parsing
$MONTH_NAMES = [
    'january' => 1, 'jan' => 1, 'february' => 2, 'feb' => 2, 'march' => 3, 'mar' => 3,
    'april' => 4, 'apr' => 4, 'may' => 5, 'june' => 6, 'jun' => 6, 'july' => 7, 'jul' => 7,
    'august' => 8, 'aug' => 8, 'september' => 9, 'sep' => 9, 'sept' => 9, 'october' => 10, 'oct' => 10,
    'november' => 11, 'nov' => 11, 'december' => 12, 'dec' => 12
];

// Error reporting
if (DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Timezone
date_default_timezone_set('America/Toronto');

// Start session
session_name(SESSION_NAME);
session_start();

// Set session lifetime
ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
?>
