<?php
/**
 * Standalone scraper for Canadian and US anime conventions.
 * This script scrapes data from various sources and saves it to a SQLite database.
 * Can be run manually or via cron job.
 */

require_once __DIR__ . '/includes/scraper.php';

// Parse command line arguments
$options = getopt('vqhsci', ['verbose', 'quiet', 'help', 'stats', 'clear-old', 'init-db']);

$verbose = isset($options['v']) || isset($options['verbose']);
$quiet = isset($options['q']) || isset($options['quiet']);
$help = isset($options['h']) || isset($options['help']);
$stats = isset($options['s']) || isset($options['stats']);
$clearOld = isset($options['c']) || isset($options['clear-old']);
$initDb = isset($options['i']) || isset($options['init-db']);

if ($help) {
    echo "Usage: php scraper.php [OPTIONS]\n";
    echo "\n";
    echo "Options:\n";
    echo "  -v, --verbose     Enable verbose logging\n";
    echo "  -q, --quiet       Only log errors\n";
    echo "  -h, --help        Show this help message\n";
    echo "  -s, --stats       Show database statistics and exit\n";
    echo "  -c, --clear-old   Clear old events from database\n";
    echo "  -i, --init-db     Initialize database and exit\n";
    echo "\n";
    echo "Examples:\n";
    echo "  php scraper.php                 # Run scraper with default settings\n";
    echo "  php scraper.php --verbose       # Run with verbose output\n";
    echo "  php scraper.php --stats         # Show database statistics\n";
    echo "  php scraper.php --init-db       # Initialize database only\n";
    exit(0);
}

// Configure error reporting based on options
if ($quiet) {
    error_reporting(E_ERROR);
} elseif ($verbose) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(E_WARNING | E_ERROR);
}

try {
    Scraper::runScraper($initDb, $stats, $clearOld);
} catch (Exception $e) {
    error_log("Scraper failed: " . $e->getMessage());
    exit(1);
}
?>
