<?php
/**
 * Delete a user-submitted convention
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/utils.php';

requireAdmin();

$conventionId = intval($_GET['id'] ?? 0);

if ($conventionId > 0) {
    $success = Database::deleteUserConvention($conventionId);
    if ($success) {
        redirectWithMessage('dashboard.php', 'Convention deleted successfully!', 'success');
    } else {
        redirectWithMessage('dashboard.php', 'Failed to delete convention', 'error');
    }
} else {
    redirectWithMessage('dashboard.php', 'Invalid convention ID', 'error');
}
?>
