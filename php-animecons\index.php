<?php
/**
 * Main page for displaying Canadian and US anime conventions
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/includes/database.php';
require_once __DIR__ . '/includes/utils.php';

try {
    // Initialize databases if they don't exist
    Database::initDatabase();
    Database::initUserDatabase();

    // Get filter parameters
    $country = isset($_GET['country']) ? sanitizeInput($_GET['country']) : null;
    $provinceState = isset($_GET['province_state']) ? sanitizeInput($_GET['province_state']) : null;

    // Get events from main database with filters (includes ongoing events)
    $mainEvents = Database::getUpcomingEvents($country, $provinceState);

    // Get approved user-submitted events
    $userEvents = Database::getApprovedUserConventions();

    // Apply filters to user events if needed
    if ($country) {
        $userEvents = array_filter($userEvents, function($event) use ($country) {
            return $event['country'] === $country;
        });
    }
    if ($provinceState) {
        $userEvents = array_filter($userEvents, function($event) use ($provinceState) {
            return $event['province_state'] === $provinceState;
        });
    }

    // Combine and sort all events
    $allEvents = array_merge($mainEvents, $userEvents);
    usort($allEvents, function($a, $b) {
        return $a['start_date'] <=> $b['start_date'];
    });

    // Get last update time
    $lastUpdate = Database::getLastUpdateTime();

    // Get available filter options
    $filters = Database::getAvailableFilters();

    // Check if user is admin
    $isAdminUser = isAdmin();

} catch (Exception $e) {
    logError("Error loading events: " . $e->getMessage());
    $allEvents = [];
    $lastUpdate = null;
    $filters = [];
    $error = "Failed to load events from database";
    $isAdminUser = isAdmin();
}

// Handle AJAX form submission for new conventions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_convention') {
    try {
        $name = sanitizeInput($_POST['name'] ?? '');
        $dates = sanitizeInput($_POST['dates'] ?? '');
        $location = sanitizeInput($_POST['location'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $provinceState = sanitizeInput($_POST['province_state'] ?? '');
        $url = sanitizeInput($_POST['url'] ?? '');

        // Validate required fields
        $errors = validateRequired(['name', 'dates', 'location', 'country', 'province_state'], $_POST);
        
        if (!empty($errors)) {
            sendJsonResponse(['success' => false, 'message' => implode(', ', $errors)], 400);
        }

        $success = Database::saveUserConvention($name, $dates, $location, $country, $provinceState, $url);
        
        if ($success) {
            sendJsonResponse(['success' => true, 'message' => 'Convention submitted for approval!']);
        } else {
            sendJsonResponse(['success' => false, 'message' => 'Failed to save convention'], 500);
        }
            
    } catch (Exception $e) {
        logError("Error submitting convention: " . $e->getMessage());
        sendJsonResponse(['success' => false, 'message' => 'Error: ' . $e->getMessage()], 500);
    }
}

// Prepare template variables
$templateVars = [
    'events' => $allEvents,
    'last_update' => $lastUpdate,
    'filters' => $filters,
    'current_filters' => [
        'country' => $country,
        'province_state' => $provinceState
    ],
    'is_admin' => $isAdminUser,
    'error' => $error ?? null,
    'flash_message' => getFlashMessage()
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- Primary Meta Tags -->
    <title>Canadian & US Anime Conventions</title>
    <meta name="title" content="Anime Conventions & Events - Find Anime Cons Near You">
    <meta name="description" content="Discover upcoming anime conventions, cosplay events, and anime gatherings across Canada and the United States. Find anime cons by location, dates, and more.">
    <meta name="keywords" content="anime conventions, anime cons, cosplay events, anime gatherings, anime festivals, otaku events, manga conventions, anime conventions 2024, anime conventions 2025, anime events near me">
    <meta name="author" content="Anime Conventions Directory">
    <meta name="robots" content="index, follow">
    <meta name="language" content="English">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
    <meta property="og:title" content="Anime Conventions & Events - Find Anime Cons Near You">
    <meta property="og:description" content="Discover upcoming anime conventions, cosplay events, and anime gatherings across Canada and the United States. Find anime cons by location, dates, and more.">
    <meta property="og:site_name" content="Anime Conventions Directory">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
    <meta property="twitter:title" content="Anime Conventions & Events - Find Anime Cons Near You">
    <meta property="twitter:description" content="Discover upcoming anime conventions, cosplay events, and anime gatherings across Canada and the United States. Find anime cons by location, dates, and more.">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Anime Conventions Directory",
        "description": "Comprehensive directory of anime conventions and events across Canada and the United States",
        "url": "<?php echo getBaseUrl(); ?>",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "<?php echo getBaseUrl(); ?>?search={search_term_string}",
            "query-input": "required name=search_term_string"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Anime Conventions Directory",
            "description": "Your go-to resource for finding anime conventions and events"
        }
    }
    </script>
    
    <!-- Additional Structured Data for Events -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": "Anime Conventions and Events",
        "description": "List of upcoming anime conventions, cosplay events, and anime gatherings",
        "itemListElement": []
    }
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #fafafa;
            color: #1a1a1a;
            line-height: 1.5;
        }

        .admin-bar {
            background: #1f2937;
            color: white;
            padding: 8px 0;
            font-size: 0.9rem;
        }

        .admin-bar-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-bar-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .admin-badge {
            background: #dc2626;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .admin-bar a {
            color: #d1d5db;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            transition: color 0.2s;
        }

        .admin-bar a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        .admin-bar .btn {
            background: #dc2626;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            text-decoration: none;
        }

        .admin-bar .btn:hover {
            background: #b91c1c;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
    </style>
</head>
<body>
    <?php if ($isAdminUser): ?>
        <div class="admin-bar">
            <div class="admin-bar-content">
                <div class="admin-bar-left">
                    <span class="admin-badge">ADMIN</span>
                    <a href="index.php">View Conventions</a>
                    <a href="admin/dashboard.php">Dashboard</a>
                </div>
                <div class="admin-bar-right">
                    <a href="admin/logout.php" class="btn">Logout</a>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php include __DIR__ . '/templates/index_content.php'; ?>
</body>
</html>
